"""
通用元素查找器
支持配置文件驱动的元素定位和操作
"""

import asyncio
import logging
import yaml
from typing import Dict, List, Any, Optional
from selenium.webdriver.support.ui import WebDriverWait
from selenium.webdriver.support import expected_conditions as EC
from appium.webdriver.common.appiumby import AppiumBy

logger = logging.getLogger(__name__)


class ElementFinder:
    """配置驱动的元素查找器"""

    def __init__(self, config_path: str):
        """初始化元素查找器

        Args:
            config_path: 配置文件路径
        """
        self.config_path = config_path
        self.config = self._load_config()
        self.elements = self.config.get('elements', {})
        self.wait_times = self.config.get('wait_times', {})
        self.retry_config = self.config.get('retry_config', {})

    def _load_config(self) -> Dict[str, Any]:
        """加载配置文件"""
        try:
            logger.info(f"尝试加载配置文件: {self.config_path}")
            with open(self.config_path, 'r', encoding='utf-8') as f:
                config = yaml.safe_load(f)
                logger.info(f"✅ 成功加载元素配置: {self.config_path}")
                logger.info(f"配置文件包含的元素: {list(config.get('elements', {}).keys())}")
                return config
        except Exception as e:
            logger.error(f"❌ 加载配置文件失败: {self.config_path}, 错误: {e}")
            return {}

    async def find_and_click(self, driver, element_name: str, max_retries: int = 3, retry_delay: float = 3.0, **kwargs) -> bool:
        """查找并点击元素（带重试机制）

        Args:
            driver: Appium驱动
            element_name: 元素名称
            max_retries: 最大重试次数
            retry_delay: 重试间隔（秒）
            **kwargs: 模板参数（如filename等）

        Returns:
            bool: 是否成功点击
        """
        if element_name not in self.elements:
            logger.error(f"配置中未找到元素: {element_name}")
            return False

        element_configs = self.elements[element_name]
        logger.info(f"🔍 查找并点击元素: {element_name}")

        # 🔧 对于容易崩溃的元素，添加详细监控
        if element_name == "saved_music_tab":
            logger.info("🔧 开始监控 saved_music_tab 查找过程")
            try:
                # 检查当前内存状态
                import psutil
                import os
                process = psutil.Process(os.getpid())
                memory_info = process.memory_info()
                logger.info(f"📊 当前进程内存使用: {memory_info.rss / 1024 / 1024:.1f} MB")

                # 检查驱动状态
                current_activity = driver.current_activity
                logger.info(f"📱 当前活动: {current_activity}")

                # 检查页面元素数量（限制查询避免崩溃）
                try:
                    all_elements = driver.find_elements("xpath", "//*[@clickable='true']")
                    logger.info(f"📋 可点击元素数量: {len(all_elements)}")
                except Exception as element_count_error:
                    logger.warning(f"⚠️ 无法获取元素数量: {str(element_count_error)}")

            except Exception as monitor_error:
                logger.warning(f"⚠️ 监控信息获取失败: {str(monitor_error)}")

        # 重试机制
        for retry in range(max_retries):
            if retry > 0:
                logger.info(f"🔄 第 {retry + 1} 次尝试查找元素: {element_name}")
                await asyncio.sleep(retry_delay)

            for i, config in enumerate(element_configs, 1):
                try:
                    logger.info(f"🔍 方法{i}：{config.get('description', config['type'])}")

                    element = await self._find_element(driver, config, element_name, **kwargs)
                    if element:
                        # 检查是否是坐标点击（已经执行了点击操作）
                        if element == "coordinate_clicked":
                            logger.info(f"✅ 成功点击 {element_name} (方法{i}: {config['type']} - 坐标点击, 第{retry + 1}次尝试)")
                        else:
                            # 普通元素点击 - 增加详细日志和智能点击
                            try:
                                # 记录元素信息
                                element_info = f"元素类型: {type(element).__name__}"
                                if hasattr(element, 'tag_name'):
                                    element_info += f", 标签: {element.tag_name}"
                                if hasattr(element, 'get_attribute'):
                                    try:
                                        resource_id = element.get_attribute('resource-id')
                                        content_desc = element.get_attribute('content-desc')
                                        clickable = element.get_attribute('clickable')
                                        element_info += f", resource-id: {resource_id}, content-desc: {content_desc}, clickable: {clickable}"
                                    except:
                                        pass

                                logger.info(f"🎯 即将点击元素 {element_name}: {element_info}")

                                # 检查元素是否可点击
                                is_clickable = False
                                try:
                                    clickable_attr = element.get_attribute('clickable')
                                    is_clickable = clickable_attr == 'true'
                                except:
                                    is_clickable = True  # 如果无法获取属性，假设可点击

                                if not is_clickable and element_name == "privacy_button":
                                    # 对于隐私按钮，如果元素不可点击，尝试查找可点击的子元素
                                    logger.warning(f"⚠️ 元素 {element_name} 不可点击，尝试查找可点击的子元素")
                                    clickable_child = await self._find_clickable_child(element)
                                    if clickable_child:
                                        logger.info(f"✅ 找到可点击的子元素，使用子元素进行点击")
                                        element = clickable_child
                                    else:
                                        logger.warning(f"⚠️ 未找到可点击的子元素，尝试直接点击")

                                # 执行点击
                                element.click()
                                logger.info(f"✅ 成功点击 {element_name} (方法{i}: {config['type']}, 第{retry + 1}次尝试)")

                            except Exception as click_error:
                                logger.error(f"❌ 点击元素失败 {element_name}: {str(click_error)}")
                                raise click_error

                        # 等待点击后的延迟
                        wait_time = self.wait_times.get('after_click', 2)
                        await asyncio.sleep(wait_time)
                        return True

                except Exception as e:
                    error_msg = str(e)
                    logger.warning(f"❌ 方法{i}失败 ({config['type']}): {error_msg}")

                    # 对于特定元素，提供更详细的调试信息
                    if element_name == "finish_editing_button" and "NoSuchElementError" in error_msg:
                        logger.debug(f"🔍 完成按钮查找失败详情:")
                        logger.debug(f"   - 查找方式: {config['type']}")
                        logger.debug(f"   - 查找值: {config['value']}")
                        logger.debug(f"   - 超时时间: {config.get('timeout', 'N/A')}秒")

                        # 尝试查找页面上所有的Button元素
                        try:
                            all_buttons = driver.find_elements("xpath", "//android.widget.Button")
                            logger.debug(f"   - 页面上共有 {len(all_buttons)} 个Button元素")
                            for idx, btn in enumerate(all_buttons[:5]):  # 只显示前5个
                                try:
                                    btn_text = btn.get_attribute('text') or ''
                                    btn_desc = btn.get_attribute('content-desc') or ''
                                    btn_id = btn.get_attribute('resource-id') or ''
                                    logger.debug(f"     Button{idx}: text='{btn_text}', desc='{btn_desc}', id='{btn_id}'")
                                except:
                                    pass
                        except Exception as debug_error:
                            logger.debug(f"   - 调试查找失败: {str(debug_error)}")

                    continue

            # 如果这次尝试失败，等待一下再重试
            if retry < max_retries - 1:
                logger.warning(f"⚠️ 第 {retry + 1} 次尝试失败，{retry_delay} 秒后重试...")

        logger.error(f"❌ 所有方法和重试都失败: {element_name}")
        return False

    async def _find_clickable_child(self, parent_element):
        """在父元素中查找可点击的子元素"""
        try:
            # 尝试查找所有子元素
            children = parent_element.find_elements("xpath", ".//*[@clickable='true']")
            if children:
                logger.info(f"🔍 在父元素中找到 {len(children)} 个可点击的子元素")
                # 返回第一个可点击的子元素
                return children[0]
            else:
                # 如果没有找到可点击的子元素，尝试查找所有子元素
                all_children = parent_element.find_elements("xpath", ".//*")
                logger.info(f"🔍 父元素共有 {len(all_children)} 个子元素")
                for child in all_children:
                    try:
                        clickable = child.get_attribute('clickable')
                        if clickable == 'true':
                            logger.info(f"✅ 找到可点击的子元素")
                            return child
                    except:
                        continue

            return None
        except Exception as e:
            logger.warning(f"⚠️ 查找可点击子元素失败: {str(e)}")
            return None

    async def find_element(self, driver, element_name: str, wait_for_real_element: bool = False, **kwargs):
        """仅查找元素，不执行操作

        Args:
            driver: Appium驱动
            element_name: 元素名称
            wait_for_real_element: 是否只查找真实元素（跳过坐标定位）
            **kwargs: 模板参数

        Returns:
            WebElement or None: 找到的元素
        """
        if element_name not in self.elements:
            logger.error(f"配置中未找到元素: {element_name}")
            return None

        element_configs = self.elements[element_name]
        logger.info(f"🔍 查找元素: {element_name}")

        for i, config in enumerate(element_configs, 1):
            try:
                # 如果是等待真实元素，跳过坐标定位
                if wait_for_real_element and config['type'] in ['coordinate', 'coordinate_relative']:
                    logger.debug(f"⏭️ 跳过坐标定位方法{i}：{config.get('description', config['type'])}")
                    continue

                logger.debug(f"🔍 方法{i}：{config.get('description', config['type'])}")
                element = await self._find_element(driver, config, element_name, **kwargs)
                if element and element != "coordinate_clicked":
                    # 额外检查元素是否真实可见和可点击
                    if wait_for_real_element:
                        try:
                            is_displayed = element.is_displayed()
                            is_enabled = element.is_enabled()
                            if is_displayed and is_enabled:
                                logger.info(f"✅ 找到真实可见元素 {element_name} (方法{i}: {config['type']})")
                                return element
                            else:
                                logger.debug(f"⚠️ 元素存在但不可见或不可用: displayed={is_displayed}, enabled={is_enabled}")
                                continue
                        except Exception as check_error:
                            logger.debug(f"⚠️ 检查元素可见性失败: {str(check_error)}")
                            continue
                    else:
                        logger.info(f"✅ 找到元素 {element_name} (方法{i}: {config['type']})")
                        return element

            except Exception as e:
                logger.debug(f"❌ 方法{i}失败 ({config['type']}): {str(e)}")
                continue

        logger.error(f"❌ 未找到元素: {element_name}")
        return None

    async def _find_element(self, driver, config: Dict[str, Any], element_name: str = "", **kwargs):
        """根据配置查找单个元素

        Args:
            driver: Appium驱动
            config: 元素配置
            element_name: 元素名称（用于智能策略）
            **kwargs: 模板参数

        Returns:
            WebElement or None: 找到的元素
        """
        element_type = config['type']
        value = config['value']
        timeout = config.get('timeout', 10)

        # 处理模板参数
        if isinstance(value, str) and '{' in value:
            try:
                value = value.format(**kwargs)
            except KeyError as e:
                logger.warning(f"模板参数缺失: {e}")
                return None

        try:
            return await self._find_element_with_recovery(driver, element_type, value, timeout)
        except Exception as e:
            logger.debug(f"查找元素失败 ({element_type}): {str(e)}")
            return None

    async def _find_element_with_recovery(self, driver, element_type: str, value: str, timeout: int):
        """带恢复机制的元素查找"""
        max_retries = 2

        for attempt in range(max_retries):
            try:
                # 使用超时控制包装所有操作
                if element_type == "id":
                    # 🔧 智能策略：根据元素类型选择最佳查找方式
                    if self._should_prefer_parent_strategy(element_name):
                        logger.info(f"🎯 元素 {element_name} 使用父元素优先策略")
                        # 对于已知需要父元素点击的元素，直接使用父元素策略
                        try:
                            element = await asyncio.wait_for(
                                asyncio.to_thread(
                                    lambda: WebDriverWait(driver, timeout).until(
                                        EC.presence_of_element_located((AppiumBy.ID, value))
                                    )
                                ),
                                timeout=timeout + 5.0
                            )
                            # 立即查找可点击的父元素
                            clickable_parent = self._find_clickable_parent(element)
                            if clickable_parent:
                                logger.info(f"🎯 父元素策略成功：找到可点击父元素")
                                return clickable_parent
                            else:
                                logger.info(f"🎯 父元素策略回退：使用原元素")
                                return element
                        except:
                            raise
                    else:
                        # 对于其他元素，使用传统策略
                        try:
                            # 方法1：尝试找到可点击的元素
                            return await asyncio.wait_for(
                                asyncio.to_thread(
                                    lambda: WebDriverWait(driver, timeout).until(
                                        EC.element_to_be_clickable((AppiumBy.ID, value))
                                    )
                                ),
                                timeout=timeout + 5.0
                            )
                        except:
                            # 方法2：如果元素不可点击，尝试查找可点击的父元素
                            try:
                                element = await asyncio.wait_for(
                                    asyncio.to_thread(
                                        lambda: WebDriverWait(driver, timeout).until(
                                            EC.presence_of_element_located((AppiumBy.ID, value))
                                        )
                                    ),
                                    timeout=timeout + 5.0
                                )
                                # 查找可点击的父元素
                                clickable_parent = self._find_clickable_parent(element)
                                if clickable_parent:
                                    logger.info(f"🎯 传统策略回退成功：找到可点击父元素")
                                    return clickable_parent
                                else:
                                    logger.warning(f"⚠️ 未找到可点击父元素，返回原元素")
                                    return element
                            except:
                                raise
                elif element_type == "xpath" or element_type == "xpath_template":
                    return await asyncio.wait_for(
                        asyncio.to_thread(
                            lambda: WebDriverWait(driver, timeout).until(
                                EC.element_to_be_clickable((AppiumBy.XPATH, value))
                            )
                        ),
                        timeout=timeout + 5.0
                    )
                elif element_type == "accessibility_id":
                    return await asyncio.wait_for(
                        asyncio.to_thread(
                            lambda: WebDriverWait(driver, timeout).until(
                                EC.element_to_be_clickable((AppiumBy.ACCESSIBILITY_ID, value))
                            )
                        ),
                        timeout=timeout + 5.0
                    )
                elif element_type == "coordinate":
                    # 坐标点击不需要查找元素，直接执行点击
                    await asyncio.wait_for(
                        asyncio.to_thread(lambda: driver.tap([value])),
                        timeout=10.0
                    )
                    return "coordinate_clicked"  # 返回特殊标识表示坐标点击已完成
                elif element_type == "coordinate_relative":
                    # 相对坐标点击
                    screen_size = await asyncio.wait_for(
                        asyncio.to_thread(lambda: driver.get_window_size()),
                        timeout=10.0
                    )
                    x = int(screen_size['width'] * value[0])
                    y = int(screen_size['height'] * value[1])
                    await asyncio.wait_for(
                        asyncio.to_thread(lambda: driver.tap([(x, y)])),
                        timeout=10.0
                    )
                    return "coordinate_clicked"  # 返回特殊标识表示坐标点击已完成
                else:
                    logger.error(f"不支持的元素类型: {element_type}")
                    return None

            except Exception as e:
                error_msg = str(e).lower()
                logger.info(f"🔍 查找元素时遇到错误: {str(e)}")

                # 检查是否是UiAutomator2连接问题
                if any(keyword in error_msg for keyword in [
                    'instrumentation process is not running',
                    'uiautomator2 server',
                    'probably crashed',
                    'unknown server-side error',
                    'cannot be proxied to uiautomator2 server'
                ]):
                    logger.warning(f"🔄 检测到UiAutomator2连接问题 (第{attempt + 1}次)")

                    # 🔧 记录崩溃事件到分析器
                    try:
                        from .crash_analyzer import get_crash_analyzer
                        analyzer = get_crash_analyzer()

                        # 尝试获取当前任务信息
                        task_id = getattr(driver, '_task_id', 'unknown')
                        step_name = getattr(driver, '_current_step', 'unknown')
                        task_progress = getattr(driver, '_task_progress', 0)

                        analyzer.record_crash(
                            task_id=task_id,
                            step_name=step_name,
                            element_name=element_name,  # 这个变量在函数参数中定义
                            task_progress=task_progress,
                            error_message=error_msg
                        )
                    except Exception as analyzer_error:
                        logger.debug(f"崩溃分析器记录失败: {str(analyzer_error)}")

                    if attempt < max_retries - 1:
                        # 简单等待重试，大多数情况下这就足够了
                        logger.info("🔧 等待5秒后重试，可能是临时问题")
                        await asyncio.sleep(5)
                        continue
                    else:
                        # 只有在最后一次重试时才尝试恢复
                        logger.warning("❌ 多次重试失败，尝试恢复UiAutomator2连接")
                        recovery_success = await self._gentle_recover_uiautomator2(driver)
                        if recovery_success:
                            logger.info("✅ UiAutomator2恢复成功，最后一次重试")
                            await asyncio.sleep(3)
                            continue
                        else:
                            logger.error("❌ UiAutomator2恢复失败")
                            raise e
                else:
                    # 其他类型的错误，直接抛出
                    raise e

        return None

    async def _gentle_recover_uiautomator2(self, driver) -> bool:
        """轻量级UiAutomator2恢复，不重启应用"""
        try:
            logger.info("🔧 开始轻量级UiAutomator2恢复")

            # 方法1: 连接测试 + 简单元素查找验证
            try:
                logger.info("🔧 方法1: 连接测试")
                # 先测试基本连接
                await asyncio.wait_for(
                    asyncio.to_thread(lambda: driver.current_activity),
                    timeout=5.0
                )

                # 再测试元素查找能力（这是真正的问题所在）
                logger.info("🔧 验证元素查找能力...")
                await asyncio.wait_for(
                    asyncio.to_thread(lambda: driver.find_elements("xpath", "//*")),
                    timeout=8.0
                )
                logger.info("✅ 方法1成功：连接和元素查找都正常")
                return True
            except Exception as e1:
                logger.info(f"🔧 方法1失败: {str(e1)}")

            # 方法2: 获取窗口大小来激活连接
            try:
                logger.info("🔧 方法2: 获取窗口大小激活连接")
                await asyncio.wait_for(
                    asyncio.to_thread(lambda: driver.get_window_size()),
                    timeout=5.0
                )
                # 等待一下让连接稳定
                await asyncio.sleep(2)
                # 测试元素查找能力
                await asyncio.wait_for(
                    asyncio.to_thread(lambda: driver.find_elements("xpath", "//*")),
                    timeout=8.0
                )
                logger.info("✅ 方法2成功：连接已激活且元素查找正常")
                return True
            except Exception as e2:
                logger.info(f"🔧 方法2失败: {str(e2)}")

            # 方法3: 尝试获取页面源码并重新激活
            try:
                logger.info("🔧 方法3: 获取页面源码并重新激活")
                await asyncio.wait_for(
                    asyncio.to_thread(lambda: driver.page_source),
                    timeout=10.0
                )
                # 等待更长时间让UiAutomator2重新稳定
                await asyncio.sleep(3)
                # 最终测试元素查找能力
                await asyncio.wait_for(
                    asyncio.to_thread(lambda: driver.find_elements("xpath", "//*")),
                    timeout=10.0
                )
                logger.info("✅ 方法3成功：页面源码获取成功且元素查找正常")
                return True
            except Exception as e3:
                logger.info(f"🔧 方法3失败: {str(e3)}")

            logger.info("⚠️ 轻量级恢复失败，需要完整恢复")
            return False

        except Exception as e:
            logger.warning(f"⚠️ 轻量级恢复过程异常: {str(e)}")
            return False

    async def _restart_uiautomator2_service(self, driver) -> bool:
        """重启UiAutomator2服务（通过ADB命令）"""
        try:
            logger.info("🔧 开始重启UiAutomator2服务")

            # 方法1: 通过ADB重启UiAutomator2服务
            try:
                import subprocess

                # 获取设备ID
                device_id = None
                # 尝试多种方式获取设备ID
                try:
                    device_id = getattr(driver, 'udid', None)
                    if not device_id:
                        device_id = getattr(driver, 'device_id', None)
                    if not device_id:
                        # 尝试从capabilities获取
                        caps = getattr(driver, 'capabilities', {})
                        device_id = caps.get('udid') or caps.get('deviceName')
                    if not device_id:
                        # 尝试从session获取
                        session_caps = getattr(driver, 'session', {})
                        if hasattr(session_caps, 'get'):
                            device_id = session_caps.get('udid') or session_caps.get('deviceName')
                except Exception as get_id_error:
                    logger.debug(f"获取设备ID时出错: {str(get_id_error)}")

                if not device_id:
                    logger.warning("无法获取设备ID，跳过ADB重启")
                    raise Exception("No device ID")

                logger.info(f"🔧 通过ADB重启设备 {device_id} 的UiAutomator2服务")

                # 停止UiAutomator2服务
                stop_cmd = f"adb -s {device_id} shell am force-stop io.appium.uiautomator2.server"
                process = await asyncio.create_subprocess_shell(
                    stop_cmd,
                    stdout=asyncio.subprocess.PIPE,
                    stderr=asyncio.subprocess.PIPE
                )
                await asyncio.wait_for(process.communicate(), timeout=10.0)
                await asyncio.sleep(2)

                # 停止UiAutomator2测试服务
                stop_test_cmd = f"adb -s {device_id} shell am force-stop io.appium.uiautomator2.server.test"
                process2 = await asyncio.create_subprocess_shell(
                    stop_test_cmd,
                    stdout=asyncio.subprocess.PIPE,
                    stderr=asyncio.subprocess.PIPE
                )
                await asyncio.wait_for(process2.communicate(), timeout=10.0)
                await asyncio.sleep(3)

                # 测试连接恢复
                await asyncio.wait_for(
                    asyncio.to_thread(lambda: driver.find_elements("xpath", "//*")),
                    timeout=15.0
                )

                logger.info("✅ ADB重启UiAutomator2服务成功")
                return True

            except Exception as adb_error:
                logger.warning(f"⚠️ ADB重启失败: {str(adb_error)}")
                return False

        except Exception as e:
            logger.warning(f"⚠️ 重启UiAutomator2服务异常: {str(e)}")
            return False

    async def _recover_uiautomator2(self, driver) -> bool:
        """完整恢复UiAutomator2服务（保守策略）"""
        try:
            logger.info("🔧 开始完整UiAutomator2恢复")

            # 方法1: 尝试简单的元素查找来激活服务
            try:
                logger.info("🔧 方法1: 简单元素查找激活服务")
                # 查找一个简单的元素来激活UiAutomator2
                await asyncio.wait_for(
                    asyncio.to_thread(lambda: driver.find_elements("xpath", "//*")),
                    timeout=10.0
                )
                await asyncio.sleep(2)

                # 测试连接
                await asyncio.wait_for(
                    asyncio.to_thread(lambda: driver.current_activity),
                    timeout=5.0
                )
                logger.info("✅ 方法1成功：元素查找激活完成")
                return True
            except Exception as e1:
                logger.warning(f"⚠️ 方法1失败: {str(e1)}")

            # 方法2: 重新启动当前活动（不重启整个应用）
            try:
                logger.info("🔧 方法2: 重新启动当前活动")
                current_activity = None
                try:
                    # 先尝试获取当前活动
                    current_activity = driver.current_activity
                except:
                    # 如果获取失败，使用默认活动
                    current_activity = "com.google.android.apps.youtube.app.WatchWhileActivity"

                await asyncio.wait_for(
                    asyncio.to_thread(
                        lambda: driver.start_activity(
                            "com.google.android.youtube",
                            current_activity
                        )
                    ),
                    timeout=15.0
                )
                await asyncio.sleep(3)

                # 测试连接
                await asyncio.wait_for(
                    asyncio.to_thread(lambda: driver.current_activity),
                    timeout=5.0
                )
                logger.info("✅ 方法2成功：活动重启完成")
                return True
            except Exception as e2:
                logger.warning(f"⚠️ 方法2失败: {str(e2)}")

            # 方法3: 最后才尝试应用重启（只在前面方法都失败时）
            logger.warning("🔧 前面方法都失败，尝试应用重启（这可能会中断当前操作）")
            try:
                logger.info("🔧 方法3: 应用重启（带超时控制）")

                # 终止应用（带超时）
                logger.info("🔧 终止YouTube应用")
                await asyncio.wait_for(
                    asyncio.to_thread(lambda: driver.terminate_app("com.google.android.youtube")),
                    timeout=10.0
                )
                await asyncio.sleep(3)

                # 启动应用（带超时）
                logger.info("🔧 启动YouTube应用")
                await asyncio.wait_for(
                    asyncio.to_thread(lambda: driver.activate_app("com.google.android.youtube")),
                    timeout=15.0
                )
                await asyncio.sleep(5)

                # 测试连接
                await asyncio.wait_for(
                    asyncio.to_thread(lambda: driver.current_activity),
                    timeout=8.0
                )
                logger.info("✅ 方法3成功：应用重启完成")
                return True
            except asyncio.TimeoutError:
                logger.warning("⚠️ 方法3超时：应用重启操作超时")
            except Exception as e3:
                logger.warning(f"⚠️ 方法3失败: {str(e3)}")

            logger.error("❌ 所有恢复方法都失败了")
            return False

        except Exception as e:
            logger.error(f"❌ UiAutomator2恢复过程异常: {str(e)}")
            return False

    async def input_text(self, driver, element_name: str, text: str) -> bool:
        """在指定元素中输入文本

        Args:
            driver: Appium驱动
            element_name: 元素名称
            text: 要输入的文本

        Returns:
            bool: 是否成功输入
        """
        element = await self.find_element(driver, element_name)
        if element:
            try:
                element.clear()
                element.send_keys(text)
                logger.info(f"✅ 成功输入文本到 {element_name}: {text}")
                return True
            except Exception as e:
                logger.error(f"❌ 输入文本失败 {element_name}: {str(e)}")
                return False
        return False

    def get_wait_time(self, wait_type: str) -> float:
        """获取等待时间

        Args:
            wait_type: 等待类型

        Returns:
            float: 等待时间（秒）
        """
        return self.wait_times.get(wait_type, 2.0)

    def _find_clickable_parent(self, element):
        """查找可点击的父元素

        Args:
            element: 当前元素

        Returns:
            WebElement: 可点击的父元素，如果找不到则返回None
        """
        try:
            current = element
            max_levels = 3  # 最多向上查找3级父元素

            for level in range(max_levels):
                if current.get_attribute("clickable") == "true":
                    logger.info(f"🎯 找到可点击的父元素（第{level}级）")
                    return current

                # 查找父元素
                try:
                    current = current.find_element(AppiumBy.XPATH, "./..")
                except:
                    logger.debug(f"无法找到第{level+1}级父元素")
                    break

            logger.debug("未找到可点击的父元素")
            return None

        except Exception as e:
            logger.debug(f"查找可点击父元素异常: {str(e)}")
            return None

    def _should_prefer_parent_strategy(self, element_name: str) -> bool:
        """判断是否应该优先使用父元素策略

        Args:
            element_name: 元素名称

        Returns:
            bool: 是否优先使用父元素策略
        """
        # 这些元素通常需要点击父元素
        parent_preferred_elements = [
            "finish_editing_button",  # 完成按钮
            "saved_music_tab",        # 已保存标签页
            "add_music_to_video_button",  # 添加音乐按钮
        ]

        return element_name in parent_preferred_elements
