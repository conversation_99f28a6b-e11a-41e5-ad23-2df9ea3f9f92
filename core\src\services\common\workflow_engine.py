"""
工作流引擎
支持配置文件驱动的自动化工作流执行
"""

import asyncio
import logging
import yaml
from typing import Dict, Any, Optional, Callable

from .element_finder import ElementFinder

# 导入Keys用于键盘操作
try:
    from selenium.webdriver.common.keys import Keys
except ImportError:
    # 如果无法导入，定义一个简单的替代
    class Keys:
        ENTER = "\u000D"
        RETURN = "\u000D"
        BACKSPACE = "\u0008"

# 导入Android键盘操作
try:
    from appium.webdriver.common.appiumby import AppiumBy
    from appium.webdriver.extensions.android.nativekey import AndroidKey
except ImportError:
    # 如果无法导入，定义一个简单的替代
    class AndroidKey:
        ENTER = 66
        SEARCH = 84
        BACK = 4
        DEL = 67

logger = logging.getLogger(__name__)


class WorkflowEngine:
    """配置驱动的工作流引擎"""

    def __init__(self, elements_config_path: str):
        """初始化工作流引擎

        Args:
            elements_config_path: 元素配置文件路径
        """
        self.element_finder = ElementFinder(elements_config_path)
        self.current_workflow = None
        self.current_step_index = 0
        self.workflow_context = {}
        self.progress_callback: Optional[Callable] = None
        self.status_callback: Optional[Callable] = None

    def set_progress_callback(self, callback: Callable):
        """设置进度回调函数"""
        self.progress_callback = callback

    def set_status_callback(self, callback: Callable):
        """设置状态回调函数"""
        self.status_callback = callback

    async def _send_enter_key_via_adb(self, driver, method_name="") -> bool:
        """使用ADB发送回车键"""
        try:
            # 获取设备ID
            device_id = getattr(driver, 'desired_capabilities', {}).get('udid') or getattr(driver, 'capabilities', {}).get('udid')
            if not device_id:
                # 尝试通过adb devices获取设备ID
                try:
                    import subprocess
                    result = subprocess.run(['adb', 'devices'], capture_output=True, text=True, timeout=5)
                    if result.returncode == 0:
                        lines = result.stdout.strip().split('\n')[1:]  # 跳过标题行
                        for line in lines:
                            if 'device' in line:
                                device_id = line.split()[0]
                                break
                except:
                    pass

            if device_id:
                logger.info(f"🔍 {method_name}: 使用ADB发送回车键到设备: {device_id}")
                import subprocess
                adb_cmd = ['adb', '-s', device_id, 'shell', 'input', 'keyevent', '66']
                result = subprocess.run(adb_cmd, capture_output=True, text=True, timeout=10)
                if result.returncode == 0:
                    logger.info(f"✅ {method_name}: ADB回车键发送成功")
                    return True
                else:
                    logger.warning(f"⚠️ {method_name}: ADB回车键发送失败: {result.stderr}")
            else:
                logger.warning(f"⚠️ {method_name}: 无法获取设备ID，跳过ADB方法")

        except Exception as adb_e:
            logger.warning(f"⚠️ {method_name}: ADB方法失败: {str(adb_e)}")

        return False

    def load_workflow(self, workflow_path: str) -> bool:
        """加载工作流配置

        Args:
            workflow_path: 工作流配置文件路径

        Returns:
            bool: 是否加载成功
        """
        try:
            logger.info(f"加载工作流配置: {workflow_path}")
            with open(workflow_path, 'r', encoding='utf-8') as f:
                self.current_workflow = yaml.safe_load(f)

            workflow_info = self.current_workflow.get('workflow', {})
            logger.info(f"✅ 成功加载工作流: {workflow_info.get('name', 'Unknown')}")
            logger.info(f"工作流描述: {workflow_info.get('description', 'No description')}")
            logger.info(f"工作流版本: {workflow_info.get('version', 'Unknown')}")
            steps = workflow_info.get('steps', [])
            logger.info(f"工作流步骤数: {len(steps)}")

            # 打印所有步骤的名称以便调试
            for i, step in enumerate(steps):
                logger.info(f"  步骤{i+1}: {step.get('name', 'Unknown')} (id: {step.get('id', 'Unknown')}, action: {step.get('action', 'Unknown')})")

            self.current_step_index = 0
            self.workflow_context = {}
            return True

        except Exception as e:
            logger.error(f"❌ 加载工作流配置失败: {workflow_path}, 错误: {e}")
            return False

    async def execute_workflow(self, driver, **parameters) -> bool:
        """执行工作流

        Args:
            driver: Appium驱动
            **parameters: 工作流参数

        Returns:
            bool: 是否执行成功
        """
        if not self.current_workflow:
            logger.error("❌ 没有加载工作流配置")
            return False

        try:
            workflow_info = self.current_workflow['workflow']
            steps = workflow_info.get('steps', [])
            config = self.current_workflow.get('config', {})

            logger.info(f"🚀 开始执行工作流: {workflow_info.get('name')}")
            logger.info(f"总步骤数: {len(steps)}")

            # 更新工作流上下文
            self.workflow_context.update(parameters)

            # 执行每个步骤
            for i, step in enumerate(steps):
                self.current_step_index = i
                step_name = step.get('name', f'Step {i+1}')
                step_id = step.get('id', 'Unknown')
                step_action = step.get('action', 'Unknown')
                step_required = step.get('required', True)

                logger.info(f"📋 执行步骤 {i+1}/{len(steps)}: {step_name}")
                logger.info(f"🔧 步骤详情 - ID: {step_id}, 动作: {step_action}, 必需: {step_required}")

                # 更新进度
                progress = int((i / len(steps)) * 100)
                self._update_progress(progress, f"执行步骤: {step_name}")

                # 执行步骤
                try:
                    success = await self._execute_step(driver, step, config)
                    logger.info(f"🔍 步骤执行结果: {step_name} -> {'成功' if success else '失败'}")

                    if not success:
                        if step_required:
                            logger.error(f"❌ 必需步骤失败: {step_name} (ID: {step_id})")
                            self._update_status("error", f"步骤失败: {step_name}")
                            return False
                        else:
                            logger.warning(f"⚠️ 可选步骤失败，继续执行: {step_name} (ID: {step_id})")
                            continue

                    logger.info(f"✅ 步骤完成: {step_name}")
                except Exception as step_error:
                    error_msg = str(step_error)
                    error_type = type(step_error).__name__

                    logger.error(f"❌ 步骤执行异常: {step_name} (ID: {step_id}) - {error_msg}")
                    logger.error(f"❌ 错误类型: {error_type}")

                    # 记录详细的步骤错误信息
                    detailed_error = f"步骤 '{step_name}' 执行失败: {error_type} - {error_msg}"

                    # 根据错误类型提供更具体的建议
                    if "timeout" in error_msg.lower() or "时间" in error_msg:
                        detailed_error += "\n建议: 检查网络连接或增加超时时间"
                    elif "element" in error_msg.lower() or "元素" in error_msg:
                        detailed_error += "\n建议: 检查应用界面是否正常加载，或更新元素定位配置"
                    elif "connection" in error_msg.lower() or "连接" in error_msg:
                        detailed_error += "\n建议: 检查设备连接状态和Appium服务"
                    elif "permission" in error_msg.lower() or "权限" in error_msg:
                        detailed_error += "\n建议: 检查应用权限设置"

                    if step_required:
                        self._update_status("error", detailed_error)
                        return False
                    else:
                        logger.warning(f"⚠️ 可选步骤异常，继续执行: {step_name}")
                        continue

                # 等待
                wait_time = step.get('wait_after', 0)
                if wait_time > 0:
                    logger.info(f"⏳ 等待 {wait_time} 秒...")
                    await asyncio.sleep(wait_time)

            # 工作流完成
            logger.info("🎉 工作流执行完成")
            self._update_progress(100, "工作流执行完成")
            self._update_status("completed", "工作流执行成功")
            return True

        except Exception as e:
            error_msg = str(e)
            error_type = type(e).__name__

            logger.error(f"❌ 工作流执行异常: {error_msg}", exc_info=True)

            # 构建详细的错误信息
            detailed_error = f"工作流执行失败: {error_type} - {error_msg}"

            # 根据错误类型提供具体建议
            if "timeout" in error_msg.lower() or "超时" in error_msg:
                detailed_error += "\n建议: 检查网络连接和设备响应速度"
            elif "connection" in error_msg.lower() or "连接" in error_msg:
                detailed_error += "\n建议: 检查Appium服务和设备连接状态"
            elif "element" in error_msg.lower() or "元素" in error_msg:
                detailed_error += "\n建议: 检查应用界面状态和元素定位配置"
            elif "permission" in error_msg.lower() or "权限" in error_msg:
                detailed_error += "\n建议: 检查应用权限和系统设置"
            else:
                detailed_error += "\n建议: 检查设备状态、应用版本和配置文件"

            self._update_status("error", detailed_error)
            return False

    async def _execute_step(self, driver, step: Dict[str, Any], config: Dict[str, Any]) -> bool:
        """执行单个步骤

        Args:
            driver: Appium驱动
            step: 步骤配置
            config: 工作流配置

        Returns:
            bool: 是否执行成功
        """
        step_name = step.get('name', 'Unknown')
        action = step.get('action')
        max_retries = 3

        # 检查步骤条件，如果不满足则跳过
        if not await self._check_step_condition(step):
            logger.info(f"⏭️ 跳过步骤（条件不满足）: {step_name}")
            return True  # 条件不满足时返回True，表示步骤"成功"跳过

        # 添加Appium连接恢复机制
        for attempt in range(max_retries):
            try:
                if attempt > 0:
                    logger.info(f"🔄 重试步骤 {step_name}，第 {attempt + 1} 次尝试")

                return await self._execute_step_action(driver, step, config)

            except Exception as e:
                error_msg = str(e)

                # 检查是否是Appium连接问题
                if self._is_appium_connection_error(error_msg):
                    logger.warning(f"⚠️ 检测到Appium连接问题: {error_msg}")

                    if attempt < max_retries - 1:  # 不是最后一次尝试
                        logger.info(f"🔧 尝试恢复Appium连接... (尝试 {attempt + 1}/{max_retries})")

                        if await self._recover_appium_connection(driver):
                            logger.info("✅ Appium连接恢复成功，重试步骤")
                            await asyncio.sleep(2)  # 等待连接稳定
                            continue
                        else:
                            logger.warning("❌ Appium连接恢复失败")
                    else:
                        logger.error(f"❌ 达到最大重试次数，步骤失败: {step_name}")
                        return False
                else:
                    # 非连接问题，直接抛出
                    logger.error(f"❌ 执行步骤异常: {str(e)}")
                    return False

        return False

    def _is_appium_connection_error(self, error_msg: str) -> bool:
        """检查是否是Appium连接错误"""
        connection_error_keywords = [
            "instrumentation process is not running",
            "probably crashed",
            "UiAutomator2 server",
            "cannot be proxied",
            "UnknownError",
            "server-side error",
            "AccessibilityNodeInfo",
            "Timed out",
            "Connection refused",
            "Session not found",
            "Original error"
        ]

        return any(keyword in error_msg for keyword in connection_error_keywords)

    async def _recover_appium_connection(self, driver) -> bool:
        """恢复Appium连接"""
        try:
            logger.info("🔧 开始Appium连接恢复流程...")

            # 方法1: 测试当前连接
            try:
                logger.info("🔧 方法1: 测试当前连接...")
                _ = driver.current_activity
                logger.info("✅ 连接测试成功，无需恢复")
                return True
            except:
                logger.info("🔧 连接测试失败，继续恢复流程")

            # 方法2: 重启应用
            try:
                logger.info("🔧 方法2: 重启YouTube应用...")
                driver.terminate_app("com.google.android.youtube")
                await asyncio.sleep(3)
                driver.activate_app("com.google.android.youtube")
                await asyncio.sleep(5)

                # 验证恢复
                _ = driver.current_activity
                logger.info("✅ 应用重启成功，连接恢复")
                return True
            except Exception as e:
                logger.warning(f"⚠️ 应用重启失败: {str(e)}")

            # 方法3: 尝试获取页面源码来"唤醒"连接
            try:
                logger.info("🔧 方法3: 尝试唤醒连接...")
                _ = driver.page_source
                await asyncio.sleep(2)
                _ = driver.current_activity
                logger.info("✅ 连接唤醒成功")
                return True
            except Exception as e:
                logger.warning(f"⚠️ 连接唤醒失败: {str(e)}")

            logger.error("❌ 所有Appium连接恢复方法都失败了")
            return False

        except Exception as e:
            logger.error(f"❌ Appium连接恢复异常: {str(e)}")
            return False

    async def _execute_step_action(self, driver, step: Dict[str, Any], config: Dict[str, Any]) -> bool:
        """执行步骤动作（原始逻辑）"""
        try:
            action = step.get('action')
            element_name = step.get('element')
            step_id = step.get('id')

            logger.info(f"🔧 执行动作: {action}")

            if action == "click":
                return await self._execute_click_action(driver, step)
            elif action == "click_if_exists":
                return await self._execute_click_if_exists_action(driver, step)
            elif action == "input_text":
                return await self._execute_input_action(driver, step)
            elif action == "select_option":
                return await self._execute_select_action(driver, step)
            elif action == "wait_for_completion":
                return await self._execute_wait_action(driver, step)
            elif action == "wait_for_element":
                return await self._execute_wait_for_element_action(driver, step)
            elif action == "select_music":
                return await self._execute_select_music_action(driver, step, config)
            # handle_music 动作已被配置化的步骤替代
            # handle_audio_settings 动作已被配置化的步骤替代
            # handle_publish_schedule 动作已被配置化的步骤替代
            elif action == "adjust_slider":
                return await self._execute_adjust_slider_action(driver, step)
            elif action == "select_music_item":
                return await self._execute_select_music_item_action(driver, step)
            elif action == "input_time":
                return await self._execute_input_time_action(driver, step)
            elif action == "conditional":
                return await self._execute_conditional_action(driver, step)
            elif action == "select_privacy":
                return await self._execute_select_privacy_action(driver, step)
            else:
                logger.error(f"❌ 不支持的动作类型: {action}")
                return False

        except Exception as e:
            logger.error(f"❌ 执行步骤动作异常: {str(e)}")
            raise e  # 重新抛出异常，让上层处理

    async def _execute_click_action(self, driver, step: Dict[str, Any]) -> bool:
        """执行点击动作"""
        element_name = step.get('element')
        parameters = self._get_step_parameters(step)

        # 🔧 对于隐私设置按钮，添加特殊验证
        if element_name == "privacy_button":
            logger.info("🔒 执行隐私设置按钮点击，添加验证机制")
            return await self._execute_privacy_button_click(driver, step)

        # 🔧 对于容易崩溃的步骤，添加预警检查
        if element_name == "saved_music_tab":
            logger.warning("⚠️ 即将执行容易崩溃的步骤: saved_music_tab")

            # 执行预防性健康检查
            try:
                # 检查 ADB 连接状态
                from .adb_manager import get_adb_manager
                adb_manager = get_adb_manager()

                # 获取设备ID（尝试多种方式）
                device_id = getattr(driver, 'udid', None) or getattr(driver, 'device_id', None)
                if device_id:
                    adb_connected = await adb_manager.ensure_device_connected(device_id)
                    if not adb_connected:
                        logger.error(f"❌ ADB 连接检查失败，设备: {device_id}")
                        logger.error("🚨 ADB 连接异常，saved_music_tab 操作可能失败")
                    else:
                        logger.info(f"✅ ADB 连接检查通过，设备: {device_id}")

                # 快速检查驱动状态
                driver_status = driver.current_activity
                logger.info(f"🔧 驱动状态检查通过: {driver_status}")

                # 短暂等待，让系统稳定
                await asyncio.sleep(2)

            except Exception as health_error:
                logger.error(f"❌ 驱动状态检查失败: {str(health_error)}")
                logger.error("🚨 检测到驱动异常，saved_music_tab 操作可能失败")

                # 检查是否是 ADB 相关错误
                if "device" in str(health_error).lower() and "not found" in str(health_error).lower():
                    logger.error("🚨 确认是 ADB 连接问题！")

                # 继续执行，但已记录预警

        return await self.element_finder.find_and_click(driver, element_name, **parameters)

    async def _execute_privacy_button_click(self, driver, step: Dict[str, Any]) -> bool:
        """执行隐私设置按钮点击，带验证机制"""
        element_name = step.get('element')
        parameters = self._get_step_parameters(step)

        logger.info("🔒 开始隐私设置按钮点击验证流程")

        try:
            # 1. 记录点击前的界面状态
            try:
                current_activity = driver.current_activity
                logger.info(f"🔍 点击前当前Activity: {current_activity}")
            except Exception as e:
                logger.warning(f"⚠️ 无法获取当前Activity: {str(e)}")

            # 2. 执行点击操作
            logger.info("🔒 执行隐私设置按钮点击")
            click_success = await self.element_finder.find_and_click(driver, element_name, **parameters)

            if not click_success:
                logger.error("❌ 隐私设置按钮点击失败")
                return False

            # 3. 等待界面加载
            await asyncio.sleep(2)  # 等待界面加载

            logger.info("✅ 隐私设置按钮点击完成")
            return True

        except Exception as e:
            logger.error(f"❌ 隐私设置按钮点击验证过程异常: {str(e)}")
            return False

    async def _execute_click_if_exists_action(self, driver, step: Dict[str, Any]) -> bool:
        """执行条件点击动作（如果元素存在则点击）"""
        element_name = step.get('element')
        parameters = self._get_step_parameters(step)
        step_name = step.get('name', 'Unknown')

        logger.info(f"🔍 检查元素是否存在: {element_name}")

        try:
            # 尝试查找元素，但不要求必须找到
            element = await self.element_finder.find_element(driver, element_name, **parameters)
            if element:
                # 如果找到元素，则点击
                success = await self.element_finder.find_and_click(driver, element_name, **parameters)
                if success:
                    logger.info(f"✅ 元素存在并成功点击: {step_name}")
                    return True
                else:
                    logger.warning(f"⚠️ 元素存在但点击失败: {step_name}")
                    return False
            else:
                # 如果没有找到元素，这是正常情况（可选步骤）
                logger.info(f"ℹ️ 元素不存在，跳过步骤: {step_name}")
                return True

        except Exception as e:
            # 对于可选步骤，异常也被视为正常情况
            logger.info(f"ℹ️ 元素不存在或查找异常，跳过步骤: {step_name} - {str(e)}")
            return True

    async def _execute_input_action(self, driver, step: Dict[str, Any]) -> bool:
        """执行输入动作"""
        element_name = step.get('element')
        parameters = self._get_step_parameters(step)

        # 获取要输入的文本
        text_param = None
        for param in step.get('parameters', []):
            param_name = param.get('name')
            if param_name in self.workflow_context:
                text_param = self.workflow_context[param_name]
                break

        if text_param is None:
            logger.warning(f"⚠️ 没有找到输入文本参数")
            return True  # 对于可选的输入，返回True

        # 特殊处理YouTube Shorts标题输入
        if element_name == 'title_input':
            logger.info(f"🔧 YouTube Shorts标题输入: {text_param}")
            return await self._input_shorts_title(driver, text_param)

        return await self.element_finder.input_text(driver, element_name, text_param)

    async def _input_shorts_title(self, driver, title_text: str) -> bool:
        """专门处理YouTube Shorts标题输入"""
        try:
            logger.info(f"🔧 开始输入Shorts标题: {title_text}")

            # 方法1: 尝试通过元素查找器找到标题输入框
            title_element = await self.element_finder.find_element(driver, "title_input")
            if title_element:
                logger.info("✅ 找到标题输入框，点击激活")
                return await self._perform_title_input(title_element, title_text)

            # 方法2: 尝试直接查找各种可能的标题输入元素
            logger.info("🔧 方法2: 尝试直接查找标题输入元素")
            title_selectors = [
                "//android.view.View[@text='为你的短视频添加标题']",
                "//*[@text='为你的短视频添加标题']",
                "//*[@hint='为你的短视频添加标题']",
                "//*[contains(@text, '添加标题')]",
                "//*[contains(@hint, '添加标题')]",
                "//android.widget.EditText",
                "//*[@clickable='true' and contains(@bounds, '301') and contains(@bounds, '377')]"
            ]

            for selector in title_selectors:
                try:
                    logger.info(f"🔍 尝试选择器: {selector}")
                    elements = driver.find_elements("xpath", selector)
                    if elements:
                        for element in elements:
                            try:
                                # 检查元素是否可见和可点击
                                if element.is_displayed() and element.is_enabled():
                                    logger.info(f"✅ 找到可用的标题输入元素")
                                    return await self._perform_title_input(element, title_text)
                            except Exception as elem_e:
                                logger.debug(f"元素检查失败: {str(elem_e)}")
                                continue
                except Exception as sel_e:
                    logger.debug(f"选择器失败: {str(sel_e)}")
                    continue

            # 方法3: 使用坐标点击
            logger.info("🔧 方法3: 使用坐标点击标题输入区域")
            try:
                # 点击标题输入区域的坐标
                driver.tap([(547, 339)])
                await asyncio.sleep(2)

                # 尝试查找激活后的输入框
                active_inputs = driver.find_elements("xpath", "//android.widget.EditText[@focused='true']")
                if not active_inputs:
                    active_inputs = driver.find_elements("xpath", "//android.widget.EditText")

                if active_inputs:
                    logger.info("✅ 坐标点击成功，找到激活的输入框")
                    return await self._perform_title_input(active_inputs[0], title_text)
                else:
                    # 直接使用ADB输入
                    logger.info("🔧 尝试直接使用ADB输入标题")
                    driver.set_value(title_text)
                    await asyncio.sleep(1)
                    logger.info(f"✅ 使用ADB输入标题成功: {title_text}")
                    return True

            except Exception as coord_e:
                logger.warning(f"⚠️ 坐标点击方法失败: {str(coord_e)}")

            logger.error("❌ 所有标题输入方法都失败了")
            return False

        except Exception as e:
            logger.error(f"❌ 输入Shorts标题失败: {str(e)}")
            return False

    async def _perform_title_input(self, element, title_text: str) -> bool:
        """执行标题输入操作"""
        try:
            logger.info("🔧 开始执行标题输入操作")

            # 点击激活输入框
            element.click()
            await asyncio.sleep(1)

            # 清空现有内容
            try:
                element.clear()
                await asyncio.sleep(0.5)
                logger.info("✅ 成功清空输入框")
            except:
                # 如果clear失败，尝试全选删除
                try:
                    element.send_keys(Keys.CONTROL + "a")
                    await asyncio.sleep(0.5)
                    element.send_keys(Keys.DELETE)
                    await asyncio.sleep(0.5)
                    logger.info("✅ 使用全选删除清空输入框")
                except:
                    logger.warning("⚠️ 无法清空标题输入框，直接输入")

            # 输入标题
            element.send_keys(title_text)
            await asyncio.sleep(1)

            # 验证输入是否成功
            try:
                current_text = element.get_attribute('text')
                if current_text and title_text in current_text:
                    logger.info(f"✅ 标题输入验证成功: {current_text}")
                else:
                    logger.warning(f"⚠️ 标题输入验证失败，当前文本: {current_text}")
            except:
                logger.info("ℹ️ 无法验证输入内容，假设输入成功")

            logger.info(f"✅ 成功输入标题: {title_text}")
            return True

        except Exception as e:
            logger.error(f"❌ 执行标题输入操作失败: {str(e)}")
            return False

    async def _execute_select_action(self, driver, step: Dict[str, Any]) -> bool:
        """执行选择动作"""
        element_name = step.get('element')

        # 特殊处理隐私设置选择
        if element_name == 'privacy_button':
            return await self._select_privacy_option(driver, step)



        # 其他选择动作的通用逻辑
        logger.info("🔧 执行选择动作（通用逻辑）")
        return True

    async def _select_privacy_option(self, driver, step: Dict[str, Any]) -> bool:
        """选择隐私设置选项"""
        try:
            # 从工作流上下文中获取隐私设置
            privacy_setting = self.workflow_context.get('privacy', 'public')
            logger.info(f"🔒 开始设置隐私选项: {privacy_setting}")

            # 首先点击隐私设置按钮
            logger.info("🔒 点击隐私设置按钮")
            privacy_button_success = await self.element_finder.find_and_click(driver, "privacy_button")
            if not privacy_button_success:
                logger.error("❌ 无法点击隐私设置按钮")
                return False

            await asyncio.sleep(2)  # 等待隐私选项界面加载

            # 根据隐私设置选择对应的选项
            privacy_element_map = {
                'public': 'privacy_option_public',
                'unlisted': 'privacy_option_unlisted',
                'private': 'privacy_option_private'
            }

            privacy_element = privacy_element_map.get(privacy_setting.lower(), 'privacy_option_public')

            logger.info(f"🔒 选择隐私选项: {privacy_setting} -> {privacy_element}")
            option_success = await self.element_finder.find_and_click(driver, privacy_element)

            if option_success:
                logger.info(f"✅ 成功设置隐私选项为: {privacy_setting}")
                await asyncio.sleep(1)  # 等待选项生效
                return True
            else:
                logger.warning(f"⚠️ 无法选择隐私选项: {privacy_setting}")
                # 如果无法选择指定选项，尝试选择默认的公开选项
                if privacy_setting.lower() != 'public':
                    logger.info("🔒 尝试选择默认的公开选项")
                    default_success = await self.element_finder.find_and_click(driver, "privacy_option_public")
                    if default_success:
                        logger.info("✅ 成功设置为默认的公开选项")
                        return True

                logger.error("❌ 无法设置任何隐私选项")
                return False

        except Exception as e:
            logger.error(f"❌ 隐私设置选择异常: {str(e)}")
            return False



    async def _execute_wait_action(self, driver, step: Dict[str, Any]) -> bool:
        """执行等待动作"""
        step_id = step.get('id')
        timeout = step.get('timeout', 60)
        description = step.get('description', 'Unknown')

        logger.info(f"🔧 执行等待动作: {description}")

        if step_id == "wait_upload":
            # 等待上传完成的逻辑
            logger.info(f"等待上传完成，最大等待时间: {timeout}秒")
            await asyncio.sleep(timeout)  # 简化为固定等待时间
            logger.info("✅ 上传等待完成")
            return True
        elif step_id == "wait_video_processing":
            # 等待视频处理完成的逻辑
            logger.info(f"⏳ 等待YouTube视频处理完成，等待时间: {timeout}秒")
            logger.info("YouTube正在处理视频，请耐心等待...")

            # 分段等待，每5秒报告一次进度
            total_waited = 0
            while total_waited < timeout:
                wait_chunk = min(5, timeout - total_waited)
                await asyncio.sleep(wait_chunk)
                total_waited += wait_chunk

                progress = int((total_waited / timeout) * 100)
                logger.info(f"视频处理进度: {total_waited}/{timeout}秒 ({progress}%)")

                # 更新进度回调
                if self.progress_callback:
                    self.progress_callback(50 + int(progress * 0.1), f"等待视频处理: {progress}%")

            logger.info("✅ 视频处理等待完成")
            return True
        else:
            # 通用等待逻辑
            wait_time = min(timeout, 10)  # 最多等待10秒
            logger.info(f"等待 {wait_time} 秒...")
            await asyncio.sleep(wait_time)
            logger.info("✅ 等待完成")
            return True

    async def _execute_wait_for_element_action(self, driver, step: Dict[str, Any]) -> bool:
        """等待元素出现"""
        element_name = step.get('element')
        timeout = step.get('timeout', 30)  # 默认超时时间保持30秒
        description = step.get('description', 'Unknown')

        logger.info(f"🔍 等待元素出现: {element_name}")
        logger.info(f"⏳ 最大等待时间: {timeout}秒")
        logger.info(f"📋 等待描述: {description}")

        start_time = asyncio.get_event_loop().time()
        # 🔧 大幅降低查找频率，避免UiAutomator2崩溃
        if element_name == "add_music_button" and timeout > 60:
            # 对于视频处理等待，使用更长的检查间隔
            check_interval = 30  # 每30秒检查一次（进一步降低频率）
            health_check_interval = 90  # 每90秒进行一次健康检查
            logger.info("🔧 使用视频处理专用等待策略（超低频查找）")
        elif element_name == "add_music_to_video_button":
            # 对于添加音乐按钮，使用快速失败策略
            check_interval = 2  # 每2秒检查一次，但快速失败
            health_check_interval = 10  # 每10秒进行一次健康检查
            logger.info("🔧 使用音乐按钮专用等待策略（快速失败）")
        else:
            check_interval = 10  # 普通等待每10秒检查一次
            health_check_interval = 30  # 每30秒进行一次健康检查

        last_progress_report = 0  # 上次进度报告的时间
        consecutive_failures = 0  # 连续失败次数
        max_consecutive_failures = 3  # 最大连续失败次数

        while True:
            try:
                # 定期进行连接健康检查，防止长时间无操作导致UiAutomator2崩溃
                elapsed_time = asyncio.get_event_loop().time() - start_time
                if elapsed_time - last_progress_report >= health_check_interval:
                    logger.info(f"🔧 进行连接健康检查 (已等待 {int(elapsed_time)} 秒)")
                    try:
                        # 执行轻量级操作保持连接活跃
                        _ = driver.current_activity
                        _ = driver.get_window_size()
                        logger.info("✅ 连接健康检查通过")
                    except Exception as health_e:
                        logger.warning(f"⚠️ 连接健康检查失败: {health_e}")
                        # 尝试恢复连接
                        recovery_success = await self._recover_appium_connection(driver)
                        if not recovery_success:
                            logger.error("❌ 连接恢复失败，停止等待")
                            return False
                    last_progress_report = elapsed_time

                # 🔧 对于视频处理等待，使用最简单的查找方式
                if element_name == "add_music_button" and timeout > 60:
                    # 只使用最简单的ID查找，避免复杂的xpath和多重尝试
                    try:
                        element = await asyncio.wait_for(
                            asyncio.to_thread(
                                lambda: driver.find_element("id", "com.google.android.youtube:id/shorts_camera_music_button")
                            ),
                            timeout=2.0  # 短超时，快速失败
                        )
                        if element:
                            elapsed_time = int(asyncio.get_event_loop().time() - start_time)
                            logger.info(f"✅ 视频处理完成，添加音乐按钮已出现 (等待了 {elapsed_time} 秒)")
                            return True
                    except Exception:
                        # 静默失败，继续等待（这是正常的，因为视频还在处理）
                        pass
                else:
                    # 普通元素等待使用完整的查找逻辑
                    try:
                        element = await self.element_finder.find_element(driver, element_name, wait_for_real_element=True)
                        if element:
                            elapsed_time = int(asyncio.get_event_loop().time() - start_time)
                            logger.info(f"✅ 元素已出现: {element_name} (等待了 {elapsed_time} 秒)")
                            consecutive_failures = 0  # 重置失败计数
                            return True
                        else:
                            consecutive_failures += 1
                    except Exception as find_error:
                        consecutive_failures += 1
                        logger.warning(f"⚠️ 查找元素时出错: {str(find_error)}")

                    # 🔧 对于容易导致崩溃的元素，快速失败
                    if element_name == "add_music_to_video_button" and consecutive_failures >= max_consecutive_failures:
                        logger.warning(f"⚠️ 元素 {element_name} 连续 {consecutive_failures} 次查找失败，提前退出避免崩溃")
                        return False

                # 检查是否超时
                elapsed_time = asyncio.get_event_loop().time() - start_time
                if elapsed_time >= timeout:
                    logger.error(f"❌ 等待元素超时: {element_name} (等待了 {int(elapsed_time)} 秒)")
                    return False

                # 等待一段时间再检查
                progress = int((elapsed_time / timeout) * 100)

                # 每30秒报告一次进度，避免日志过于频繁
                if elapsed_time - last_progress_report >= 30:
                    logger.info(f"🔍 继续等待元素出现: {element_name} ({int(elapsed_time)}/{timeout}秒, {progress}%)")
                    last_progress_report = elapsed_time

                    # 更新进度回调
                    if self.progress_callback:
                        self.progress_callback(50 + int(progress * 0.1), f"等待元素出现: {progress}%")

                await asyncio.sleep(check_interval)

            except Exception as e:
                # 元素未找到是正常情况，继续等待
                elapsed_time = asyncio.get_event_loop().time() - start_time
                if elapsed_time >= timeout:
                    logger.error(f"❌ 等待元素超时: {element_name} - {str(e)}")
                    return False

                await asyncio.sleep(check_interval)

    async def _execute_select_music_action(self, driver, step: Dict[str, Any], config: Dict[str, Any]) -> bool:
        """执行音乐选择动作"""
        try:
            # 从工作流上下文中获取选中的音乐
            selected_music = self.workflow_context.get('selectedMusic', [])

            if not selected_music:
                logger.info("ℹ️ 未选择音乐，跳过音乐选择步骤")
                return True

            logger.info(f"🎵 开始选择音乐，共 {len(selected_music)} 首")

            # 如果选择了多首音乐，随机选择一首
            import random
            if len(selected_music) > 1:
                music = random.choice(selected_music)
                logger.info(f"🎲 从 {len(selected_music)} 首音乐中随机选择了: {music.get('title', '')} ({music.get('music_id', '')})")
            else:
                music = selected_music[0]
                logger.info(f"🎵 选择唯一的音乐: {music.get('title', '')} ({music.get('music_id', '')})")

            music_title = music.get('title', '')
            music_id = music.get('music_id', '')

            logger.info(f"🎵 最终选择的音乐: {music_title} ({music_id})")

            # 点击搜索框
            logger.info("🔍 点击音乐搜索框")
            search_success = await self.element_finder.find_and_click(driver, "music_search_box")
            if not search_success:
                logger.error("❌ 无法点击音乐搜索框")
                return False

            await asyncio.sleep(2)

            # 输入搜索关键词并直接按回车（一步完成）
            search_keyword = music_id if music_id else music_title
            logger.info(f"🔍 搜索音乐: {search_keyword}")

            # 检查驱动状态，如果有问题则尝试恢复
            try:
                # 简单的驱动状态检查
                current_activity = driver.current_activity
                logger.info(f"🔍 当前活动: {current_activity}")
            except Exception as driver_check_e:
                logger.warning(f"⚠️ 驱动状态检查失败: {str(driver_check_e)}")
                logger.info("🔧 尝试恢复驱动连接...")
                try:
                    # 尝试重新获取页面源码来"唤醒"驱动
                    driver.page_source
                    logger.info("✅ 驱动连接已恢复")
                except Exception as recovery_e:
                    logger.error(f"❌ 驱动恢复失败: {str(recovery_e)}")
                    return False

            # 方法1：使用标准的send_keys + Keys.ENTER（推荐）
            search_success = False
            try:
                logger.info("🔍 方法1: 使用send_keys + Keys.ENTER")
                search_input = await self.element_finder.find_element(driver, "music_search_input")
                if search_input:
                    # 先清空输入框
                    search_input.clear()
                    await asyncio.sleep(0.5)

                    # 一步完成输入+回车（使用Keys.ENTER）
                    search_input.send_keys(search_keyword + Keys.ENTER)
                    logger.info(f"✅ 成功使用Keys.ENTER输入并搜索: {search_keyword}")

                    # 等待一下，检查是否有搜索效果
                    await asyncio.sleep(3)

                    # 检查是否真的有搜索结果
                    try:
                        # 尝试查找音乐项来验证搜索是否生效
                        music_items = driver.find_elements("xpath", "//*[contains(@text, 'KSA') or contains(@content-desc, 'KSA')]")
                        if music_items and len(music_items) > 0:
                            logger.info(f"✅ 检测到 {len(music_items)} 个可能的搜索结果")
                            search_success = True
                        else:
                            logger.warning("⚠️ 未检测到音乐搜索结果，尝试其他方法")
                            # 检查页面源码
                            page_source = driver.page_source
                            if search_keyword in page_source:
                                logger.info("✅ 在页面源码中找到搜索关键词")
                                search_success = True
                            else:
                                logger.warning("⚠️ 页面源码中也没有搜索关键词")
                    except Exception as check_e:
                        logger.warning(f"⚠️ 无法检查搜索效果: {str(check_e)}")
                        # 如果检查失败，不认为搜索成功，继续尝试其他方法

                else:
                    logger.warning("⚠️ 无法找到搜索输入框")
            except Exception as e:
                logger.warning(f"⚠️ Keys.ENTER方法失败: {str(e)}")

            # 方法2：尝试强力搜索触发方式
            if not search_success:
                try:
                    logger.info("🔍 方法2: 尝试强力搜索触发方式")
                    search_input = await self.element_finder.find_element(driver, "music_search_input")
                    if search_input:
                        # 确保输入框有焦点
                        search_input.click()
                        await asyncio.sleep(0.5)

                        # 方式1：使用现代的Android键盘方法
                        logger.info("⌨️ 尝试现代Android键盘方法...")
                        try:
                            # 使用driver.press_key(AndroidKey.ENTER) - 推荐方法
                            if hasattr(driver, 'press_key'):
                                driver.press_key(AndroidKey.ENTER)
                                await asyncio.sleep(1)
                                logger.info("✅ 使用driver.press_key(AndroidKey.ENTER)发送回车键")
                            else:
                                logger.warning("⚠️ 驱动不支持press_key方法")
                                raise AttributeError("Driver does not support press_key")
                        except Exception as android_key_e:
                            logger.warning(f"⚠️ AndroidKey方法失败: {str(android_key_e)}")
                            try:
                                # 备用方法：直接在元素上发送回车
                                search_input.send_keys(Keys.ENTER)
                                await asyncio.sleep(1)
                                logger.info("✅ 直接在元素上发送回车键")
                            except Exception as direct_e:
                                logger.warning(f"⚠️ 直接发送回车也失败: {str(direct_e)}")

                        # 方式2：尝试点击搜索按钮
                        logger.info("🔍 尝试查找搜索按钮...")
                        try:
                            # 查找可能的搜索按钮
                            search_buttons = [
                                "//android.widget.Button[contains(@text, '搜索')]",
                                "//android.widget.Button[contains(@content-desc, 'search')]",
                                "//android.widget.ImageButton[contains(@content-desc, 'search')]",
                                "//android.widget.ImageView[contains(@content-desc, 'search')]",
                                "//*[contains(@resource-id, 'search_button')]",
                                "//*[contains(@resource-id, 'search_icon')]"
                            ]

                            for xpath in search_buttons:
                                try:
                                    search_btn = driver.find_element("xpath", xpath)
                                    if search_btn and search_btn.is_enabled():
                                        search_btn.click()
                                        logger.info(f"✅ 找到并点击了搜索按钮: {xpath}")
                                        await asyncio.sleep(2)
                                        search_success = True
                                        break
                                except:
                                    continue

                        except Exception as btn_e:
                            logger.warning(f"⚠️ 搜索按钮查找失败: {str(btn_e)}")

                        # 方式3：尝试失去焦点再获得焦点
                        if not search_success:
                            logger.info("🔍 尝试失去焦点再获得焦点...")
                            try:
                                # 点击其他地方
                                driver.tap([(500, 300)])
                                await asyncio.sleep(0.5)
                                # 重新点击搜索框
                                search_input.click()
                                await asyncio.sleep(0.5)
                                # 再次尝试回车
                                driver.press_keycode(66)
                                await asyncio.sleep(2)
                                logger.info("✅ 完成失去焦点再获得焦点操作")
                            except Exception as focus_e:
                                logger.warning(f"⚠️ 焦点操作失败: {str(focus_e)}")

                        # 不要假设成功，让最终验证来决定
                    else:
                        logger.warning("⚠️ 无法找到搜索输入框")
                except Exception as e:
                    logger.warning(f"⚠️ 强力搜索触发方式失败: {str(e)}")

            # 方法3：尝试Android特有的搜索方式
            if not search_success:
                try:
                    logger.info("🔍 方法3: 尝试Android特有的搜索方式")

                    # 方式1：尝试发送特殊搜索字符
                    logger.info("⌨️ 尝试发送特殊搜索字符...")
                    try:
                        search_input = await self.element_finder.find_element(driver, "music_search_input")
                        if search_input:
                            # 尝试发送搜索相关的特殊字符
                            search_input.send_keys("\u0084")  # 对应KEYCODE_SEARCH的Unicode
                            await asyncio.sleep(2)
                            logger.info("✅ 尝试了搜索字符")
                    except Exception as search_char_e:
                        logger.warning(f"⚠️ 搜索字符方式失败: {str(search_char_e)}")

                    # 方式2：尝试隐藏键盘然后显示
                    logger.info("⌨️ 尝试隐藏键盘然后显示...")
                    try:
                        driver.hide_keyboard()
                        await asyncio.sleep(1)
                        # 重新点击搜索框
                        search_input = await self.element_finder.find_element(driver, "music_search_input")
                        if search_input:
                            search_input.click()
                            await asyncio.sleep(1)
                            # 尝试发送回车键
                            if hasattr(driver, 'press_key'):
                                driver.press_key(AndroidKey.ENTER)
                                logger.info("✅ 使用press_key发送回车键")
                            else:
                                search_input.send_keys(Keys.ENTER)
                                logger.info("✅ 使用send_keys发送回车键")
                            await asyncio.sleep(2)
                            logger.info("✅ 完成隐藏键盘操作")
                    except Exception as hide_e:
                        logger.warning(f"⚠️ 隐藏键盘操作失败: {str(hide_e)}")

                    # 方式3：尝试多次发送回车键
                    logger.info("⌨️ 尝试多次发送回车键...")
                    try:
                        search_input = await self.element_finder.find_element(driver, "music_search_input")
                        if search_input:
                            # 多次发送回车键
                            for i in range(3):
                                if hasattr(driver, 'press_key'):
                                    driver.press_key(AndroidKey.ENTER)
                                else:
                                    search_input.send_keys(Keys.ENTER)
                                await asyncio.sleep(0.5)
                            logger.info("✅ 尝试了多次回车键")
                    except Exception as multi_enter_e:
                        logger.warning(f"⚠️ 多次回车键失败: {str(multi_enter_e)}")

                    # 方式4：尝试发送特殊字符
                    logger.info("⌨️ 尝试发送特殊字符...")
                    try:
                        search_input = await self.element_finder.find_element(driver, "music_search_input")
                        if search_input:
                            # 尝试发送空格字符（有些搜索框用空格触发搜索）
                            search_input.send_keys(" ")
                            await asyncio.sleep(1)
                            # 删除空格（使用退格键）
                            search_input.send_keys(Keys.BACKSPACE)
                            await asyncio.sleep(1)
                            logger.info("✅ 尝试了空格字符方式")
                    except Exception as space_e:
                        logger.warning(f"⚠️ 空格字符方式失败: {str(space_e)}")

                    # 不要假设成功，让最终验证来决定

                except Exception as e:
                    logger.warning(f"⚠️ Android特有搜索方式失败: {str(e)}")

            # 方法4：最终验证搜索结果
            logger.info("🔍 最终验证搜索结果...")
            await asyncio.sleep(3)  # 给时间让搜索生效

            # 最终检查是否有真实的搜索结果
            final_check_success = False
            try:
                # 检查1：查找包含搜索关键词的元素
                music_elements = driver.find_elements("xpath", f"//*[contains(@text, '{search_keyword}') or contains(@content-desc, '{search_keyword}')]")
                if music_elements and len(music_elements) > 0:
                    logger.info(f"✅ 最终检测到 {len(music_elements)} 个包含搜索关键词的元素")
                    final_check_success = True
                else:
                    # 检查2：查找音乐相关的通用元素
                    music_generic = driver.find_elements("xpath", "//*[contains(@text, 'Last Time') or contains(@text, 'White Color') or contains(@text, 'KSA')]")
                    if music_generic and len(music_generic) > 0:
                        logger.info(f"✅ 最终检测到 {len(music_generic)} 个音乐相关元素")
                        final_check_success = True
                    else:
                        # 检查3：查找任何可能的音乐列表项
                        list_items = driver.find_elements("xpath", "//android.widget.TextView[contains(@text, 'Time') or contains(@text, 'Color')]")
                        if list_items and len(list_items) > 0:
                            logger.info(f"✅ 最终检测到 {len(list_items)} 个可能的音乐列表项")
                            final_check_success = True
                        else:
                            logger.warning("⚠️ 最终未检测到任何音乐搜索结果")

            except Exception as final_check_e:
                logger.warning(f"⚠️ 最终搜索结果检查失败: {str(final_check_e)}")

            # 只有真正检测到搜索结果才认为成功
            if final_check_success:
                search_success = True
                logger.info("🎉 搜索确认成功，找到了搜索结果")
            else:
                search_success = False
                logger.warning("❌ 搜索失败，未找到任何搜索结果")

            logger.info(f"🔍 搜索处理完成，最终状态: {'成功' if search_success else '失败'}")

            # 尝试点击搜索结果
            logger.info("🎵 尝试选择搜索结果")

            # 先尝试通过音乐标题查找
            if music_title:
                music_found = await self.element_finder.find_and_click(
                    driver, "music_item", music_title=music_title
                )
                if music_found:
                    logger.info(f"✅ 成功选择音乐: {music_title}")
                    return True

            # 再尝试通过音乐ID查找
            if music_id:
                music_found = await self.element_finder.find_and_click(
                    driver, "music_item", music_id=music_id
                )
                if music_found:
                    logger.info(f"✅ 成功选择音乐: {music_id}")
                    return True

            logger.warning(f"⚠️ 未找到指定音乐: {search_keyword}")
            return False

        except Exception as e:
            logger.error(f"❌ 音乐选择异常: {str(e)}")
            return False

    async def _execute_handle_music_action(self, driver, step: Dict[str, Any], config: Dict[str, Any]) -> bool:
        """执行音乐处理动作（包括点击添加音效和选择音乐）"""
        try:
            # 从工作流上下文中获取选中的音乐
            selected_music = self.workflow_context.get('selectedMusic', [])

            logger.info(f"🔍 工作流上下文: {list(self.workflow_context.keys())}")
            logger.info(f"🔍 从工作流上下文获取的音乐数据: {selected_music}")

            if not selected_music:
                logger.info("ℹ️ 未选择音乐，跳过整个音乐处理流程")
                return True

            logger.info(f"🎵 检测到选择了 {len(selected_music)} 首音乐，开始音乐处理流程")

            # 步骤1：点击添加音效按钮
            logger.info("🎵 步骤1: 点击添加音效按钮")
            click_success = await self.element_finder.find_and_click(driver, "add_music_button")
            if not click_success:
                logger.error("❌ 无法点击添加音效按钮")
                return False

            await asyncio.sleep(3)  # 等待音乐选择界面加载

            # 步骤2：选择音乐
            logger.info("🎵 步骤2: 选择音乐")

            # 如果选择了多首音乐，随机选择一首
            import random
            if len(selected_music) > 1:
                music = random.choice(selected_music)
                logger.info(f"🎲 从 {len(selected_music)} 首音乐中随机选择了: {music.get('title', '')} ({music.get('music_id', '')})")
            else:
                music = selected_music[0]
                logger.info(f"🎵 选择唯一的音乐: {music.get('title', '')} ({music.get('music_id', '')})")

            music_title = music.get('title', '')
            music_id = music.get('music_id', '')

            logger.info(f"🎵 最终选择的音乐: {music_title} ({music_id})")

            # 弃用搜索逻辑，直接使用已保存音乐
            logger.info("🎵 使用已保存音乐策略（已弃用搜索功能）")
            logger.info("🎵 使用已保存音乐，点击已保存标签页")

            saved_tab_success = await self.element_finder.find_and_click(driver, "saved_music_tab")
            if not saved_tab_success:
                logger.error("❌ 无法点击已保存标签页")
                return False

            await asyncio.sleep(3)  # 等待已保存音乐列表加载

            # 智能选择已保存音乐
            logger.info("🎵 选择已保存的音乐")
            music_selected = False

            try:
                # 1. 优先查找ViewGroup类型的可点击音乐项（新格式）
                saved_music_items = driver.find_elements("xpath", "//android.view.ViewGroup[@clickable='true' and contains(@content-desc, 'Shorts')]")
                if saved_music_items and len(saved_music_items) > 0:
                    first_music = saved_music_items[0]
                    first_music.click()
                    logger.info(f"✅ 成功选择已保存音乐: {first_music.get_attribute('content-desc')}")
                    music_selected = True
                else:
                    # 2. 备用方案：查找TextView类型的音乐项（旧格式）
                    text_music_items = driver.find_elements("xpath", "//android.widget.TextView[contains(@text, 'Time') or contains(@text, 'Color') or contains(@text, 'Last') or contains(@text, 'White')]")
                    if text_music_items and len(text_music_items) > 0:
                        first_music = text_music_items[0]
                        first_music.click()
                        logger.info(f"✅ 成功选择已保存音乐: {first_music.text}")
                        music_selected = True
                    else:
                        # 3. 最后备用方案：点击任何可点击的音乐项
                        clickable_items = driver.find_elements("xpath", "//android.widget.LinearLayout[@clickable='true']")
                        if clickable_items and len(clickable_items) > 0:
                            first_clickable = clickable_items[0]
                            first_clickable.click()
                            logger.info("✅ 成功选择已保存音乐（通用方法）")
                            music_selected = True
                        else:
                            logger.warning("⚠️ 未找到任何已保存的音乐项")

                # 如果选择了音乐，等待并点击添加按钮
                if music_selected:
                    logger.info("🎵 等待音乐预览界面出现...")
                    await asyncio.sleep(2)  # 等待音乐预览界面加载

                    # 点击"将此音乐添加到你的视频中"按钮
                    add_button_found = await self.element_finder.find_and_click(driver, "add_music_to_video_button")
                    if add_button_found:
                        logger.info("✅ 成功点击添加音乐按钮")
                        await asyncio.sleep(3)  # 等待音乐添加完成

                        # 点击"前往编辑器"按钮
                        logger.info("🎵 点击前往编辑器按钮")
                        editor_button_found = await self.element_finder.find_and_click(driver, "go_to_editor_button")
                        if editor_button_found:
                            logger.info("✅ 成功点击前往编辑器按钮")
                            await asyncio.sleep(2)  # 等待进入编辑器
                            return True
                        else:
                            logger.warning("⚠️ 未找到前往编辑器按钮，可能已自动进入编辑器")
                            return True
                    else:
                        logger.warning("⚠️ 未找到添加音乐按钮，尝试直接点击前往编辑器")
                        # 如果没有找到添加音乐按钮，可能音乐已自动添加，直接点击前往编辑器
                        editor_button_found = await self.element_finder.find_and_click(driver, "go_to_editor_button")
                        if editor_button_found:
                            logger.info("✅ 成功点击前往编辑器按钮")
                            await asyncio.sleep(2)
                            return True
                        else:
                            logger.warning("⚠️ 未找到前往编辑器按钮")
                            return True
                else:
                    # 如果没有找到音乐，点击关闭按钮，然后继续前往编辑器
                    logger.warning("❌ 没有找到已保存的音乐，点击关闭按钮")
                    close_button_found = await self.element_finder.find_and_click(driver, "music_picker_close_button")
                    if close_button_found:
                        logger.info("✅ 成功点击关闭按钮")
                        await asyncio.sleep(2)  # 等待音乐选择器关闭

                        # 即使没有音乐，也要点击前往编辑器继续流程
                        logger.info("🎵 没有音乐，直接点击前往编辑器按钮")
                        editor_button_found = await self.element_finder.find_and_click(driver, "go_to_editor_button")
                        if editor_button_found:
                            logger.info("✅ 成功点击前往编辑器按钮")
                            await asyncio.sleep(2)
                            return True
                        else:
                            logger.warning("⚠️ 未找到前往编辑器按钮")
                            return False
                    else:
                        logger.warning("⚠️ 未找到关闭按钮")
                        return False

            except Exception as saved_e:
                logger.error(f"❌ 选择已保存音乐失败: {str(saved_e)}")
                return False

        except Exception as e:
            logger.error(f"❌ 音乐处理异常: {str(e)}")
            return False

    async def _execute_handle_audio_settings_action(self, driver, step: Dict[str, Any], config: Dict[str, Any]) -> bool:
        """执行音频设置动作（根据前端设置决定是否保留原声）"""
        try:
            # 首先检查是否选择了音乐
            selected_music = self.workflow_context.get('selectedMusic', [])
            if not selected_music:
                logger.info("ℹ️ 未选择音乐，跳过音频设置步骤")
                return True  # 直接返回成功，让后续的"前往编辑器"步骤处理界面跳转

            # 从工作流上下文中获取音频设置
            keep_original_audio = self.workflow_context.get('keepOriginalAudio', False)
            music_volume_percentage = self.workflow_context.get('musicVolumePercentage', 50)  # 默认50%
            original_audio_percentage = self.workflow_context.get('originalAudioPercentage', 50)  # 默认50%

            # 确保音量百分比是整数类型
            try:
                original_audio_percentage = int(original_audio_percentage) if original_audio_percentage is not None else 50
                music_volume_percentage = int(music_volume_percentage) if music_volume_percentage is not None else 50
            except (ValueError, TypeError) as type_e:
                logger.warning(f"⚠️ 音量百分比类型转换失败: {str(type_e)}")
                logger.warning(f"⚠️ 原始值 - 原声: {original_audio_percentage} (类型: {type(original_audio_percentage)})")
                logger.warning(f"⚠️ 原始值 - 背景音乐: {music_volume_percentage} (类型: {type(music_volume_percentage)})")
                original_audio_percentage = 50
                music_volume_percentage = 50

            logger.info(f"🔊 音频设置处理开始，保留原声: {keep_original_audio}")
            logger.info(f"🔊 已选择音乐: {len(selected_music)} 首")
            if keep_original_audio:
                logger.info(f"🔊 背景音乐音量设置: {music_volume_percentage}% (类型: {type(music_volume_percentage)})")
                logger.info(f"🔊 原声音量设置: {original_audio_percentage}% (类型: {type(original_audio_percentage)})")

            if keep_original_audio:
                # 如果需要保留原声，点击音量调节按钮进行音频设置
                logger.info("🔊 需要保留原声，点击音量调节按钮")
                volume_button_success = await self.element_finder.find_and_click(driver, "volume_settings_button")
                if volume_button_success:
                    logger.info("✅ 成功点击音量调节按钮")
                    await asyncio.sleep(3)  # 等待音频设置界面加载

                    # 调节原声音量
                    logger.info("🔊 调节原声音量...")
                    await self._adjust_original_audio_volume(driver, original_audio_percentage)

                    # 调节背景音乐音量
                    logger.info("🔊 调节背景音乐音量...")
                    await self._adjust_music_volume(driver, music_volume_percentage)

                    # 设置完成后等待一下
                    await asyncio.sleep(2)

                    # 点击完成按钮
                    logger.info("🔊 点击音频设置完成按钮")
                    done_button_success = await self.element_finder.find_and_click(driver, "audio_settings_done_button")
                    if done_button_success:
                        logger.info("✅ 成功点击音频设置完成按钮")
                        await asyncio.sleep(2)  # 等待音频设置界面关闭
                    else:
                        logger.warning("⚠️ 无法点击音频设置完成按钮，但继续执行")

                    logger.info("✅ 音频设置完成")
                else:
                    logger.warning("⚠️ 无法点击音量调节按钮，跳过音频设置")
            else:
                logger.info("🔊 不需要保留原声，跳过音频设置")

            # 音频设置完成，返回成功
            logger.info("✅ 音频设置步骤完成")
            return True

        except Exception as e:
            logger.error(f"❌ 音频设置异常: {str(e)}")
            return False

    async def _execute_handle_publish_schedule_action(self, driver, step: Dict[str, Any], config: Dict[str, Any]) -> bool:
        """执行发布时间设置动作"""
        try:
            # 从工作流上下文中获取发布时间设置
            publish_time = self.workflow_context.get('publishTime', None)
            is_scheduled = self.workflow_context.get('isScheduled', False)

            logger.info(f"📅 开始处理发布时间设置")
            logger.info(f"📅 是否预定发布: {is_scheduled}")
            logger.info(f"📅 发布时间: {publish_time}")

            if is_scheduled and publish_time:
                # 需要设置预定发布时间
                logger.info("📅 设置预定发布时间")

                # 点击预定时间切换按钮
                logger.info("📅 点击预定时间切换按钮")
                toggle_success = await self.element_finder.find_and_click(driver, "schedule_toggle_button")
                if not toggle_success:
                    logger.warning("⚠️ 无法点击预定时间切换按钮，尝试直接设置时间")

                await asyncio.sleep(2)  # 等待界面切换

                # 点击时间设置区域
                logger.info("📅 点击时间设置区域")
                time_setting_success = await self.element_finder.find_and_click(driver, "publish_time_setting")
                if time_setting_success:
                    logger.info("✅ 成功点击时间设置区域")
                    await asyncio.sleep(2)  # 等待时间选择器加载

                    # 这里可以添加具体的时间设置逻辑
                    # 由于时间选择器的复杂性，暂时使用默认时间
                    logger.info("📅 使用默认的预定时间设置")

                    # 点击确认按钮（如果有的话）
                    # 这里需要根据实际的时间选择器界面来实现

                    logger.info("✅ 预定发布时间设置完成")
                    return True
                else:
                    logger.warning("⚠️ 无法点击时间设置区域")
                    return False
            else:
                # 立即发布，不需要特殊设置
                logger.info("📅 使用立即发布，无需设置时间")
                return True

        except Exception as e:
            logger.error(f"❌ 发布时间设置异常: {str(e)}")
            return False

    async def _adjust_music_volume(self, driver, volume_percentage: int) -> bool:
        """调节背景音乐音量"""
        try:
            # 确保volume_percentage是整数类型
            volume_percentage = int(volume_percentage) if volume_percentage is not None else 50
            logger.info(f"🔊 开始调节背景音乐音量到 {volume_percentage}%")

            # 查找音量滑块
            volume_slider = await self.element_finder.find_element(driver, "music_volume_slider")
            if not volume_slider:
                logger.warning("⚠️ 未找到音量滑块，跳过音量调节")
                return False

            # 获取滑块的边界信息
            try:
                slider_bounds = volume_slider.rect
                slider_left = int(slider_bounds['x'])
                slider_width = int(slider_bounds['width'])
                slider_y = int(slider_bounds['y']) + int(slider_bounds['height']) // 2
            except Exception as rect_e:
                logger.error(f"❌ 获取滑块边界信息失败: {str(rect_e)}")
                return False

            # 计算目标位置（基于百分比）
            # 确保百分比在0-100范围内
            volume_percentage = max(0, min(100, volume_percentage))
            target_x = slider_left + (slider_width * volume_percentage / 100)

            logger.info(f"🔊 滑块位置: x={slider_left}, width={slider_width}, y={slider_y}")
            logger.info(f"🔊 目标位置: x={target_x}, 百分比={volume_percentage}%")

            # 使用多种方法尝试设置滑块值
            success = await self._set_slider_value(driver, volume_slider, volume_percentage, target_x, slider_y, "背景音乐")
            if not success:
                logger.warning("⚠️ 所有滑块设置方法都失败了，但继续执行")

            # 验证设置是否成功
            try:
                current_value = volume_slider.get_attribute('text')
                logger.info(f"🔊 当前音量值: {current_value}")
                if current_value and current_value.strip():
                    try:
                        current_percentage = float(current_value)
                        if abs(current_percentage - volume_percentage) <= 5:  # 允许5%的误差
                            logger.info(f"✅ 音量调节成功: {current_percentage}%")
                            return True
                        else:
                            logger.warning(f"⚠️ 音量调节可能不准确: 目标{volume_percentage}%, 实际{current_percentage}%")
                            return True  # 仍然认为成功，因为已经进行了调节
                    except ValueError as val_e:
                        logger.warning(f"⚠️ 无法解析音量值 '{current_value}': {str(val_e)}")
                        return True  # 假设调节成功
                else:
                    logger.warning("⚠️ 无法获取当前音量值，假设调节成功")
                    return True
            except Exception as verify_e:
                logger.warning(f"⚠️ 无法验证音量设置: {str(verify_e)}")
                return True  # 假设调节成功

            logger.info("✅ 音量调节完成")
            return True

        except Exception as e:
            logger.error(f"❌ 音量调节失败: {str(e)}")
            logger.error(f"❌ 详细错误信息: {type(e).__name__}: {str(e)}")
            return False

    async def _adjust_original_audio_volume(self, driver, volume_percentage: int) -> bool:
        """调节原声音量"""
        try:
            # 确保volume_percentage是整数类型
            volume_percentage = int(volume_percentage) if volume_percentage is not None else 50
            logger.info(f"🔊 开始调节原声音量到 {volume_percentage}%")

            # 查找原声音量滑块
            volume_slider = await self.element_finder.find_element(driver, "original_audio_volume_slider")
            if not volume_slider:
                logger.warning("⚠️ 未找到原声音量滑块，尝试使用通用滑块")
                # 尝试使用通用滑块定位
                try:
                    all_sliders = driver.find_elements("xpath", "//android.widget.SeekBar[@resource-id='com.google.android.youtube:id/slider']")
                    if len(all_sliders) >= 2:
                        # 假设第一个是原声滑块，第二个是背景音乐滑块
                        volume_slider = all_sliders[0]
                        logger.info("✅ 使用第一个滑块作为原声音量滑块")
                    else:
                        logger.warning("⚠️ 未找到足够的滑块，跳过原声音量调节")
                        return False
                except Exception as find_e:
                    logger.error(f"❌ 查找通用滑块失败: {str(find_e)}")
                    return False

            # 获取滑块的边界信息
            try:
                slider_bounds = volume_slider.rect
                slider_left = int(slider_bounds['x'])
                slider_width = int(slider_bounds['width'])
                slider_y = int(slider_bounds['y']) + int(slider_bounds['height']) // 2
            except Exception as rect_e:
                logger.error(f"❌ 获取原声滑块边界信息失败: {str(rect_e)}")
                return False

            # 计算目标位置（基于百分比）
            # 确保百分比在0-100范围内
            volume_percentage = max(0, min(100, volume_percentage))
            target_x = slider_left + (slider_width * volume_percentage / 100)

            logger.info(f"🔊 原声滑块位置: x={slider_left}, width={slider_width}, y={slider_y}")
            logger.info(f"🔊 原声目标位置: x={target_x}, 百分比={volume_percentage}%")

            # 使用多种方法尝试设置滑块值
            success = await self._set_slider_value(driver, volume_slider, volume_percentage, target_x, slider_y, "原声")
            if not success:
                logger.warning("⚠️ 所有原声滑块设置方法都失败了，但继续执行")

            # 验证设置是否成功
            try:
                current_value = volume_slider.get_attribute('text')
                logger.info(f"🔊 当前原声音量值: {current_value}")
                if current_value and current_value.strip():
                    try:
                        current_percentage = float(current_value)
                        if abs(current_percentage - volume_percentage) <= 5:  # 允许5%的误差
                            logger.info(f"✅ 原声音量调节成功: {current_percentage}%")
                            return True
                        else:
                            logger.warning(f"⚠️ 原声音量调节可能不准确: 目标{volume_percentage}%, 实际{current_percentage}%")
                            return True  # 仍然认为成功，因为已经进行了调节
                    except ValueError as val_e:
                        logger.warning(f"⚠️ 无法解析原声音量值 '{current_value}': {str(val_e)}")
                        return True  # 假设调节成功
                else:
                    logger.warning("⚠️ 无法获取当前原声音量值，假设调节成功")
                    return True
            except Exception as verify_e:
                logger.warning(f"⚠️ 无法验证原声音量设置: {str(verify_e)}")
                return True  # 假设调节成功

            logger.info("✅ 原声音量调节完成")
            return True

        except Exception as e:
            logger.error(f"❌ 原声音量调节失败: {str(e)}")
            logger.error(f"❌ 详细错误信息: {type(e).__name__}: {str(e)}")
            return False

    async def _set_slider_value(self, driver, slider_element, target_percentage: int, target_x: float, slider_y: int, slider_name: str = "滑块") -> bool:
        """通用滑块设置方法 - 使用Appium最佳实践

        Args:
            driver: Appium驱动
            slider_element: 滑块元素
            target_percentage: 目标百分比
            target_x: 目标X坐标
            slider_y: 滑块Y坐标
            slider_name: 滑块名称（用于日志）

        Returns:
            bool: 是否设置成功
        """
        try:
            logger.info(f"🔧 开始设置{slider_name}滑块值为 {target_percentage}%")

            # 获取滑块的详细信息
            slider_bounds = slider_element.rect
            slider_left = int(slider_bounds['x'])
            slider_width = int(slider_bounds['width'])
            slider_center_y = int(slider_bounds['y']) + int(slider_bounds['height']) // 2

            logger.info(f"🔧 {slider_name}滑块信息: left={slider_left}, width={slider_width}, centerY={slider_center_y}")

            # 方法1: 使用Appium的Actions链（推荐方法）
            try:
                logger.info(f"🔧 方法1: 使用Appium Actions链设置{slider_name}滑块")

                # 计算目标位置
                target_x_precise = slider_left + (slider_width * target_percentage / 100)

                # 使用Actions链进行精确操作
                from selenium.webdriver.common.action_chains import ActionChains
                actions = ActionChains(driver)

                # 移动到滑块元素并按下
                actions.move_to_element(slider_element)
                actions.click_and_hold()

                # 移动到目标位置
                actions.move_by_offset(int(target_x_precise - slider_left - slider_width//2), 0)

                # 释放
                actions.release()

                # 执行动作链
                actions.perform()
                await asyncio.sleep(1)

                # 验证结果
                if await self._verify_slider_value(slider_element, target_percentage, slider_name):
                    logger.info(f"✅ Actions链设置{slider_name}滑块成功")
                    return True

            except Exception as actions_e:
                logger.warning(f"⚠️ Actions链方法失败: {str(actions_e)}")

            # 方法2: 使用TouchAction（Appium专用）
            try:
                logger.info(f"🔧 方法2: 使用TouchAction设置{slider_name}滑块")

                from appium.webdriver.common.touch_action import TouchAction

                # 获取当前滑块位置
                current_text = slider_element.get_attribute('text')
                current_percentage = 50
                if current_text:
                    try:
                        current_percentage = float(current_text)
                    except:
                        pass

                current_x = slider_left + (slider_width * current_percentage / 100)
                target_x_precise = slider_left + (slider_width * target_percentage / 100)

                logger.info(f"🔧 TouchAction路径: {current_x} -> {target_x_precise}")

                # 创建TouchAction
                touch = TouchAction(driver)

                # 按下当前位置
                touch.press(x=int(current_x), y=slider_center_y)

                # 等待一下
                touch.wait(200)

                # 移动到目标位置
                touch.move_to(x=int(target_x_precise), y=slider_center_y)

                # 释放
                touch.release()

                # 执行TouchAction
                touch.perform()
                await asyncio.sleep(1)

                # 验证结果
                if await self._verify_slider_value(slider_element, target_percentage, slider_name):
                    logger.info(f"✅ TouchAction设置{slider_name}滑块成功")
                    return True

            except Exception as touch_e:
                logger.warning(f"⚠️ TouchAction方法失败: {str(touch_e)}")

            # 方法3: 使用W3C Actions（最新标准）
            try:
                logger.info(f"🔧 方法3: 使用W3C Actions设置{slider_name}滑块")

                target_x_precise = slider_left + (slider_width * target_percentage / 100)

                # 使用W3C Actions API
                actions = driver.create_action_chain()

                # 添加指针动作
                pointer = actions.pointer_action
                pointer.move_to_location(int(target_x_precise), slider_center_y)
                pointer.pointer_down()
                pointer.pause(0.1)
                pointer.pointer_up()

                # 执行动作
                actions.perform()
                await asyncio.sleep(1)

                # 验证结果
                if await self._verify_slider_value(slider_element, target_percentage, slider_name):
                    logger.info(f"✅ W3C Actions设置{slider_name}滑块成功")
                    return True

            except Exception as w3c_e:
                logger.warning(f"⚠️ W3C Actions方法失败: {str(w3c_e)}")

            # 方法4: 使用原生Android滑动手势
            try:
                logger.info(f"🔧 方法4: 使用Android原生手势设置{slider_name}滑块")

                # 获取当前值和目标值
                current_text = slider_element.get_attribute('text')
                current_percentage = 50
                if current_text:
                    try:
                        current_percentage = float(current_text)
                    except:
                        pass

                # 计算滑动距离
                current_x = slider_left + (slider_width * current_percentage / 100)
                target_x_precise = slider_left + (slider_width * target_percentage / 100)

                # 使用Android的滑动手势
                driver.swipe(
                    start_x=int(current_x),
                    start_y=slider_center_y,
                    end_x=int(target_x_precise),
                    end_y=slider_center_y,
                    duration=800  # 较慢的滑动，更自然
                )
                await asyncio.sleep(1)

                # 验证结果
                if await self._verify_slider_value(slider_element, target_percentage, slider_name):
                    logger.info(f"✅ Android手势设置{slider_name}滑块成功")
                    return True

            except Exception as gesture_e:
                logger.warning(f"⚠️ Android手势方法失败: {str(gesture_e)}")

            # 方法5: 使用多次小幅度调整（精确控制）
            try:
                logger.info(f"🔧 方法5: 使用精确调整设置{slider_name}滑块")

                success = await self._precise_slider_adjustment(driver, slider_element, target_percentage, slider_left, slider_width, slider_center_y, slider_name)
                if success:
                    return True

            except Exception as precise_e:
                logger.warning(f"⚠️ 精确调整方法失败: {str(precise_e)}")

            logger.error(f"❌ 所有{slider_name}滑块设置方法都失败了")
            return False

        except Exception as e:
            logger.error(f"❌ 设置{slider_name}滑块异常: {str(e)}")
            return False

    async def _verify_slider_value(self, slider_element, target_percentage: int, slider_name: str) -> bool:
        """验证滑块值是否设置成功"""
        try:
            current_text = slider_element.get_attribute('text')
            if current_text:
                current_value = float(current_text)
                difference = abs(current_value - target_percentage)

                logger.info(f"🔍 {slider_name}滑块验证: 目标={target_percentage}%, 实际={current_value}%, 差异={difference}%")

                if difference <= 5:  # 允许5%的误差
                    logger.info(f"✅ {slider_name}滑块值验证成功")
                    return True
                else:
                    logger.warning(f"⚠️ {slider_name}滑块值验证失败，差异过大")
                    return False
            else:
                logger.warning(f"⚠️ 无法获取{slider_name}滑块当前值")
                return False

        except Exception as e:
            logger.warning(f"⚠️ {slider_name}滑块值验证异常: {str(e)}")
            return False

    async def _precise_slider_adjustment(self, driver, slider_element, target_percentage: int, slider_left: int, slider_width: int, slider_y: int, slider_name: str) -> bool:
        """精确滑块调整方法"""
        try:
            logger.info(f"🔧 开始精确调整{slider_name}滑块")

            max_attempts = 10
            tolerance = 2  # 2%的容差

            for attempt in range(max_attempts):
                # 获取当前值
                current_text = slider_element.get_attribute('text')
                if not current_text:
                    break

                current_value = float(current_text)
                difference = target_percentage - current_value

                logger.info(f"🔧 第{attempt + 1}次调整: 当前={current_value}%, 目标={target_percentage}%, 差异={difference}%")

                # 如果已经足够接近，则成功
                if abs(difference) <= tolerance:
                    logger.info(f"✅ 精确调整成功: {current_value}%")
                    return True

                # 计算调整距离
                adjustment_ratio = difference / 100  # 转换为比例
                adjustment_distance = slider_width * adjustment_ratio

                # 限制单次调整距离
                max_single_adjustment = slider_width * 0.1  # 最多10%的滑块宽度
                if abs(adjustment_distance) > max_single_adjustment:
                    adjustment_distance = max_single_adjustment if adjustment_distance > 0 else -max_single_adjustment

                # 计算当前位置和目标位置
                current_x = slider_left + (slider_width * current_value / 100)
                target_x = current_x + adjustment_distance

                # 确保目标位置在滑块范围内
                target_x = max(slider_left, min(slider_left + slider_width, target_x))

                logger.info(f"🔧 调整滑动: {current_x} -> {target_x}")

                # 执行小幅度滑动
                driver.swipe(
                    start_x=int(current_x),
                    start_y=slider_y,
                    end_x=int(target_x),
                    end_y=slider_y,
                    duration=300
                )

                await asyncio.sleep(0.5)

            logger.warning(f"⚠️ 精确调整达到最大尝试次数")
            return False

        except Exception as e:
            logger.error(f"❌ 精确调整异常: {str(e)}")
            return False

    async def _perform_progressive_slide(self, driver, start_x: float, end_x: float, y: int, target_percentage: int, slider_name: str) -> bool:
        """执行渐进式滑动操作"""
        try:
            logger.info(f"🔧 执行{slider_name}渐进式滑动: {start_x} -> {end_x}")

            # 计算滑动距离和步数
            distance = abs(end_x - start_x)
            steps = max(3, min(10, int(distance / 20)))  # 根据距离决定步数

            logger.info(f"🔧 滑动步数: {steps}")

            # 分步滑动
            for i in range(steps):
                progress = (i + 1) / steps
                current_x = start_x + (end_x - start_x) * progress

                if i == 0:
                    # 第一步：长按开始
                    driver.long_press_xy(int(start_x), y, duration=200)
                    await asyncio.sleep(0.1)
                else:
                    # 中间步骤：滑动
                    prev_x = start_x + (end_x - start_x) * (i / steps)
                    driver.swipe(int(prev_x), y, int(current_x), y, 100)
                    await asyncio.sleep(0.1)

            # 最后一步：释放
            await asyncio.sleep(0.2)

            logger.info(f"✅ {slider_name}渐进式滑动完成")
            return True

        except Exception as e:
            logger.error(f"❌ {slider_name}渐进式滑动失败: {str(e)}")
            return False

    def _get_step_parameters(self, step: Dict[str, Any]) -> Dict[str, Any]:
        """获取步骤参数"""
        parameters = {}
        for param in step.get('parameters', []):
            param_name = param.get('name')
            if param_name in self.workflow_context:
                parameters[param_name] = self.workflow_context[param_name]
        return parameters

    def _update_progress(self, progress: int, message: str):
        """更新进度"""
        if self.progress_callback:
            self.progress_callback(progress, message)

    def _update_status(self, status: str, message: str):
        """更新状态"""
        if self.status_callback:
            self.status_callback(status, message)

    async def _check_step_condition(self, step: Dict[str, Any]) -> bool:
        """检查步骤执行条件

        Args:
            step: 步骤配置

        Returns:
            bool: 是否满足执行条件
        """
        try:
            condition = step.get('condition')
            if not condition:
                # 没有条件限制，直接执行
                return True

            step_name = step.get('name', 'Unknown')
            logger.info(f"🔍 检查步骤条件: {step_name} -> {condition}")

            # 解析条件表达式
            if "selectedMusic.length > 0" in condition:
                selected_music = self.workflow_context.get('selectedMusic', [])
                result = len(selected_music) > 0
                logger.info(f"🎵 音乐选择条件: {len(selected_music)} > 0 = {result}")
                return result

            elif "keepOriginalAudio == true" in condition:
                keep_original_audio = self.workflow_context.get('keepOriginalAudio', False)
                result = keep_original_audio is True
                logger.info(f"🔊 保留原声条件: {keep_original_audio} == true = {result}")
                return result

            elif "isScheduled == true" in condition:
                is_scheduled = self.workflow_context.get('isScheduled', False)
                result = is_scheduled is True
                logger.info(f"📅 预定发布条件: {is_scheduled} == true = {result}")
                return result

            else:
                logger.warning(f"⚠️ 不支持的条件表达式: {condition}")
                return True  # 不支持的条件默认执行

        except Exception as e:
            logger.error(f"❌ 条件检查异常: {str(e)}")
            return True  # 异常时默认执行

    async def _execute_adjust_slider_action(self, driver, step: Dict[str, Any]) -> bool:
        """执行滑块调节动作"""
        try:
            element_name = step.get('element')
            parameters = self._get_step_parameters(step)

            # 获取目标百分比
            percentage = None
            for param in parameters:
                if param.get('name') == 'percentage':
                    source = param.get('source')
                    if source:
                        percentage = self.workflow_context.get(source)
                    else:
                        percentage = param.get('value')
                    break

            if percentage is None:
                logger.warning("⚠️ 未找到滑块目标百分比，跳过调节")
                return True

            percentage = int(percentage)
            logger.info(f"🔧 调节滑块 {element_name} 到 {percentage}%")

            # 查找滑块元素
            slider_element = await self.element_finder.find_element(driver, element_name)
            if not slider_element:
                logger.warning(f"⚠️ 未找到滑块元素: {element_name}")
                return False

            # 使用现有的滑块调节逻辑
            if element_name == "music_volume_slider":
                return await self._adjust_music_volume(driver, percentage)
            elif element_name == "original_audio_volume_slider":
                return await self._adjust_original_audio_volume(driver, percentage)
            else:
                # 通用滑块调节逻辑
                return await self._adjust_generic_slider(driver, slider_element, percentage, element_name)

        except Exception as e:
            logger.error(f"❌ 滑块调节异常: {str(e)}")
            return False

    async def _execute_select_music_item_action(self, driver, step: Dict[str, Any]) -> bool:
        """执行音乐选择动作"""
        try:
            # 从工作流上下文中获取选中的音乐
            selected_music = self.workflow_context.get('selectedMusic', [])
            if not selected_music:
                logger.info("ℹ️ 未选择音乐，跳过音乐选择")
                return True

            # 选择第一个音乐（简化逻辑）
            music_info = selected_music[0]
            music_id = music_info.get('id', '')
            music_title = music_info.get('title', '未知音乐')

            logger.info(f"🎵 选择音乐: {music_title} (ID: {music_id})")

            # 🔧 修复：使用标准的find_and_click方法和music_item配置
            # 先尝试通过音乐标题查找
            music_selected = await self.element_finder.find_and_click(
                driver, "music_item", music_title=music_title
            )

            if music_selected:
                logger.info(f"✅ 成功选择音乐: {music_title}")
                return True

            # 如果通过标题找不到，尝试通过音乐ID查找
            if music_id:
                music_selected = await self.element_finder.find_and_click(
                    driver, "music_item", music_id=music_id
                )

                if music_selected:
                    logger.info(f"✅ 成功选择音乐: {music_id}")
                    return True

            # 如果都找不到，尝试在音乐列表中随机选择一个
            logger.warning(f"⚠️ 未找到指定音乐，尝试在音乐列表中随机选择")

            try:
                # 🔧 修复：使用配置文件中的music_list元素
                music_list = await self.element_finder.find_element(driver, "music_list")

                if music_list:
                    # 获取列表中的所有音乐项（ViewGroup类型的元素）
                    from appium.webdriver.common.appiumby import AppiumBy
                    music_items = music_list.find_elements(AppiumBy.CLASS_NAME, "android.view.ViewGroup")

                    if music_items:
                        # 随机选择一个音乐项
                        import random
                        selected_item = random.choice(music_items)
                        selected_item.click()
                        logger.info(f"✅ 随机选择了音乐项（共{len(music_items)}个选项）")
                        return True
                    else:
                        logger.warning("⚠️ 音乐列表中没有找到ViewGroup类型的音乐项")

                        # 尝试查找其他类型的可点击元素
                        clickable_items = music_list.find_elements(AppiumBy.XPATH, ".//*[@clickable='true']")
                        if clickable_items:
                            import random
                            selected_item = random.choice(clickable_items)
                            selected_item.click()
                            logger.info(f"✅ 随机选择了可点击音乐项（共{len(clickable_items)}个选项）")
                            return True
                        else:
                            logger.warning("⚠️ 音乐列表中没有找到可点击的音乐项")
                else:
                    logger.warning("⚠️ 未找到音乐列表容器")

            except Exception as e:
                logger.warning(f"⚠️ 随机选择音乐失败: {str(e)}")

            # 如果随机选择也失败，尝试使用配置的music_item元素
            logger.warning(f"⚠️ 随机选择失败，尝试使用配置的music_item")
            music_selected = await self.element_finder.find_and_click(driver, "music_item")

            if music_selected:
                logger.info("✅ 成功选择配置的音乐项")
                return True
            else:
                logger.warning(f"⚠️ 未找到任何音乐项")
                return False

        except Exception as e:
            logger.error(f"❌ 音乐选择异常: {str(e)}")
            return False

    async def _execute_input_time_action(self, driver, step: Dict[str, Any]) -> bool:
        """执行时间输入动作"""
        try:
            element_name = step.get('element')
            parameters = self._get_step_parameters(step)

            # 获取时间值
            time_value = None
            for param in parameters:
                if param.get('name') == 'publishTime':
                    source = param.get('source')
                    if source:
                        time_value = self.workflow_context.get(source)
                    else:
                        time_value = param.get('value')
                    break

            if not time_value:
                logger.warning("⚠️ 未找到时间值，跳过时间设置")
                return True

            logger.info(f"📅 设置发布时间: {time_value}")

            # 这里可以添加具体的时间设置逻辑
            # 目前先返回成功
            logger.info("✅ 时间设置完成（简化实现）")
            return True

        except Exception as e:
            logger.error(f"❌ 时间输入异常: {str(e)}")
            return False

    async def _execute_conditional_action(self, driver, step: Dict[str, Any]) -> bool:
        """执行条件判断动作"""
        try:
            condition = step.get('condition')
            if not condition:
                logger.info("ℹ️ 无条件限制，执行步骤")
                return True

            # 简单的条件判断逻辑
            if "selectedMusic.length > 0" in condition:
                selected_music = self.workflow_context.get('selectedMusic', [])
                result = len(selected_music) > 0
            elif "keepOriginalAudio == true" in condition:
                keep_original_audio = self.workflow_context.get('keepOriginalAudio', False)
                result = keep_original_audio is True
            elif "isScheduled == true" in condition:
                is_scheduled = self.workflow_context.get('isScheduled', False)
                result = is_scheduled is True
            else:
                logger.warning(f"⚠️ 不支持的条件: {condition}")
                result = True

            logger.info(f"🔍 条件判断 '{condition}': {result}")
            return result

        except Exception as e:
            logger.error(f"❌ 条件判断异常: {str(e)}")
            return False

    async def _adjust_generic_slider(self, driver, slider_element, percentage: int, slider_name: str) -> bool:
        """通用滑块调节方法"""
        try:
            logger.info(f"🔧 调节{slider_name}滑块到 {percentage}%")

            # 获取滑块的边界信息
            bounds = slider_element.get_attribute('bounds')
            if bounds:
                # 解析bounds字符串，格式如 "[x1,y1][x2,y2]"
                import re
                matches = re.findall(r'\[(\d+),(\d+)\]', bounds)
                if len(matches) >= 2:
                    x1, y1 = int(matches[0][0]), int(matches[0][1])
                    x2, y2 = int(matches[1][0]), int(matches[1][1])

                    # 计算滑块的中心Y坐标和目标X坐标
                    slider_y = (y1 + y2) // 2
                    slider_width = x2 - x1
                    target_x = x1 + (slider_width * percentage / 100)

                    # 执行滑块设置
                    return await self._set_slider_value(driver, slider_element, percentage, target_x, slider_y, slider_name)

            logger.warning(f"⚠️ 无法获取{slider_name}滑块边界信息")
            return False

        except Exception as e:
            logger.error(f"❌ {slider_name}滑块调节失败: {str(e)}")
            return False

    async def _execute_select_privacy_action(self, driver, step: Dict[str, Any]) -> bool:
        """执行隐私选项选择动作"""
        try:
            # 从工作流上下文中获取隐私设置
            privacy_setting = self.workflow_context.get('privacy', 'public')
            logger.info(f"🔒 开始设置隐私选项: {privacy_setting}")

            # 根据隐私设置选择对应的元素
            privacy_element_map = {
                'public': 'privacy_option_public',
                'unlisted': 'privacy_option_unlisted',
                'private': 'privacy_option_private'
            }

            privacy_element = privacy_element_map.get(privacy_setting.lower(), 'privacy_option_public')

            logger.info(f"🔒 选择隐私选项: {privacy_setting} -> {privacy_element}")
            option_success = await self.element_finder.find_and_click(driver, privacy_element)

            if option_success:
                logger.info(f"✅ 成功设置隐私选项为: {privacy_setting}")
                await asyncio.sleep(1)  # 等待选项生效
                return True
            else:
                logger.warning(f"⚠️ 无法选择隐私选项: {privacy_setting}")
                # 如果无法选择指定选项，尝试选择默认的公开选项
                if privacy_setting.lower() != 'public':
                    logger.info("🔒 尝试选择默认的公开选项")
                    default_success = await self.element_finder.find_and_click(driver, "privacy_option_public")
                    if default_success:
                        logger.info("✅ 成功设置为默认的公开选项")
                        return True

                logger.error("❌ 无法设置任何隐私选项")
                return False

        except Exception as e:
            logger.error(f"❌ 隐私选项选择异常: {str(e)}")
            return False
