"""
文件发布状态管理器
负责在视频上传完成后自动更新文件的发布状态
"""

import os
import hashlib
import logging
from typing import Optional, Dict, Any
from datetime import datetime
import aiohttp
import asyncio

logger = logging.getLogger(__name__)


class PublishStatusManager:
    """文件发布状态管理器"""
    
    def __init__(self, backend_url: str = "http://***************:8000"):
        """
        初始化发布状态管理器
        
        Args:
            backend_url: Backend服务的URL
        """
        self.backend_url = backend_url.rstrip('/')
        
    def calculate_file_md5(self, file_path: str) -> Optional[str]:
        """计算文件的MD5哈希值
        
        Args:
            file_path: 文件路径
            
        Returns:
            MD5哈希值，如果计算失败返回None
        """
        try:
            if not os.path.exists(file_path):
                logger.warning(f"文件不存在: {file_path}")
                return None
                
            hash_md5 = hashlib.md5()
            with open(file_path, "rb") as f:
                for chunk in iter(lambda: f.read(4096), b""):
                    hash_md5.update(chunk)
            
            md5_hash = hash_md5.hexdigest()
            logger.info(f"计算文件MD5成功: {file_path} -> {md5_hash}")
            return md5_hash
            
        except Exception as e:
            logger.error(f"计算文件MD5失败: {file_path}, 错误: {str(e)}")
            return None
    
    async def update_publish_status(
        self, 
        file_path: str, 
        platform: str = "youtube",
        account_name: Optional[str] = None,
        video_id: Optional[str] = None,
        video_url: Optional[str] = None,
        notes: Optional[str] = None
    ) -> bool:
        """更新文件的发布状态
        
        Args:
            file_path: 视频文件路径
            platform: 平台名称（默认youtube）
            account_name: 发布账号名称
            video_id: 平台上的视频ID
            video_url: 视频链接
            notes: 备注信息
            
        Returns:
            是否更新成功
        """
        try:
            # 计算文件MD5
            md5_hash = self.calculate_file_md5(file_path)
            if not md5_hash:
                logger.error(f"无法计算文件MD5，跳过发布状态更新: {file_path}")
                return False
            
            # 构建发布状态数据
            platform_data = {
                "platform": platform,
                "is_published": True,
                "publish_date": datetime.now().strftime("%Y-%m-%d %H:%M:%S"),
                "publish_account": account_name,
                "video_id": video_id,
                "video_url": video_url,
                "notes": notes or f"通过自动化上传于 {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}"
            }
            
            # 调用Backend API更新发布状态
            success = await self._call_backend_api(md5_hash, platform, platform_data)
            
            if success:
                logger.info(f"✅ 文件发布状态更新成功: {file_path} -> {platform}")
                return True
            else:
                logger.warning(f"⚠️ 文件发布状态更新失败: {file_path}")
                return False
                
        except Exception as e:
            logger.error(f"更新文件发布状态异常: {file_path}, 错误: {str(e)}")
            return False
    
    async def _call_backend_api(
        self, 
        md5_hash: str, 
        platform: str, 
        platform_data: Dict[str, Any]
    ) -> bool:
        """调用Backend API更新发布状态
        
        Args:
            md5_hash: 文件MD5哈希值
            platform: 平台名称
            platform_data: 平台发布数据
            
        Returns:
            是否调用成功
        """
        try:
            url = f"{self.backend_url}/api/v1/filesystem/md5-records/{md5_hash}/platform/{platform}"
            
            timeout = aiohttp.ClientTimeout(total=30)  # 30秒超时
            
            async with aiohttp.ClientSession(timeout=timeout) as session:
                async with session.put(url, json=platform_data) as response:
                    if response.status == 200:
                        result = await response.json()
                        logger.info(f"Backend API调用成功: {result.get('message', '状态更新成功')}")
                        return True
                    else:
                        error_text = await response.text()
                        logger.error(f"Backend API调用失败: HTTP {response.status}, {error_text}")
                        return False
                        
        except asyncio.TimeoutError:
            logger.error(f"Backend API调用超时: {url}")
            return False
        except Exception as e:
            logger.error(f"Backend API调用异常: {str(e)}")
            return False
    
    async def get_publish_status(self, file_path: str, platform: str = "youtube") -> Optional[Dict[str, Any]]:
        """获取文件的发布状态
        
        Args:
            file_path: 文件路径
            platform: 平台名称
            
        Returns:
            发布状态信息，如果获取失败返回None
        """
        try:
            # 计算文件MD5
            md5_hash = self.calculate_file_md5(file_path)
            if not md5_hash:
                return None
            
            url = f"{self.backend_url}/api/v1/filesystem/md5-records/{md5_hash}/platform/{platform}"
            
            timeout = aiohttp.ClientTimeout(total=10)
            
            async with aiohttp.ClientSession(timeout=timeout) as session:
                async with session.get(url) as response:
                    if response.status == 200:
                        return await response.json()
                    else:
                        logger.warning(f"获取发布状态失败: HTTP {response.status}")
                        return None
                        
        except Exception as e:
            logger.error(f"获取发布状态异常: {str(e)}")
            return None


# 全局实例
_publish_status_manager = None


def get_publish_status_manager() -> PublishStatusManager:
    """获取发布状态管理器实例"""
    global _publish_status_manager
    if _publish_status_manager is None:
        _publish_status_manager = PublishStatusManager()
    return _publish_status_manager
